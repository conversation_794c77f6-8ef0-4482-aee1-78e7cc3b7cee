#!/bin/bash

# Test all routes in the financial tracking application
BASE_URL="http://localhost:8082"
COOKIE_FILE="/tmp/test_cookies.txt"

echo "=== Financial Tracking Application Route Testing ==="
echo "Base URL: $BASE_URL"
echo

# Function to test a route
test_route() {
    local route="$1"
    local description="$2"
    local expected_code="${3:-200}"
    
    echo -n "Testing $description ($route): "
    
    if [[ "$route" == "/login" || "$route" == "/" ]]; then
        # Test without authentication
        response=$(curl -s -o /dev/null -w "%{http_code}" "$BASE_URL$route")
    else
        # Test with authentication
        response=$(curl -s -b "$COOKIE_FILE" -o /dev/null -w "%{http_code}" "$BASE_URL$route")
    fi
    
    if [[ "$response" == "$expected_code" ]]; then
        echo "✅ $response"
    elif [[ "$response" == "302" && "$expected_code" == "200" ]]; then
        echo "🔄 $response (redirect - likely auth required)"
    else
        echo "❌ $response (expected $expected_code)"
    fi
}

# Create session
echo "Creating authenticated session..."
curl -c "$COOKIE_FILE" -s "$BASE_URL/login" > /dev/null
curl -c "$COOKIE_FILE" -b "$COOKIE_FILE" -s -X POST -d "username=boss&password=password123" "$BASE_URL/auth/login" > /dev/null
echo "Session created."
echo

# Test Authentication Routes
echo "=== Authentication Routes ==="
test_route "/login" "Login Page" "200"
test_route "/" "Home Page" "302"
test_route "/auth/logout" "Logout" "302"
test_route "/auth/change-password" "Change Password" "200"
echo

# Test Members Routes
echo "=== Members Routes ==="
test_route "/index.php/members" "Members Index" "200"
test_route "/index.php/members/new" "New Member Form" "200"
test_route "/index.php/members/show/1" "Show Member 1" "200"
test_route "/index.php/members/show/2" "Show Member 2" "200"
test_route "/index.php/members/show/3" "Show Member 3" "200"
test_route "/index.php/members/edit/1" "Edit Member 1" "200"
test_route "/index.php/members/show/999" "Show Non-existent Member" "404"
echo

# Test Commitments Routes
echo "=== Commitments Routes ==="
test_route "/index.php/commitments" "Commitments Index" "200"
test_route "/index.php/commitments/new" "New Commitment Form" "200"
test_route "/index.php/commitments/create/1" "New Commitment for Member 1" "200"
test_route "/index.php/commitments/create/2" "New Commitment for Member 2" "200"
test_route "/index.php/commitments/show/1" "Show Commitment 1" "200"
test_route "/index.php/commitments/edit/1" "Edit Commitment 1" "200"
test_route "/index.php/commitments/member-commitments/1" "Member 1 Commitments" "200"
test_route "/index.php/commitments/show/999" "Show Non-existent Commitment" "404"
echo

# Test Payments Routes
echo "=== Payments Routes ==="
test_route "/index.php/payments" "Payments Index" "200"
test_route "/index.php/payments/new" "New Payment Form" "200"
test_route "/index.php/payments/show/1" "Show Payment 1" "200"
test_route "/index.php/payments/edit/1" "Edit Payment 1" "200"
test_route "/index.php/payments/member-payments/1" "Member 1 Payments" "200"
test_route "/index.php/payments/generate-receipt/1" "Generate Receipt 1" "200"
test_route "/index.php/payments/show/999" "Show Non-existent Payment" "404"
echo

# Test Reports Routes
echo "=== Reports Routes ==="
test_route "/index.php/reports" "Reports Dashboard" "200"
test_route "/index.php/reports/collection-summary" "Collection Summary" "200"
test_route "/index.php/reports/outstanding-balances" "Outstanding Balances" "200"
test_route "/index.php/reports/payment-history" "Payment History" "200"
test_route "/index.php/reports/commitment-report" "Commitment Report" "200"
test_route "/index.php/reports/statistics" "Statistics" "200"
test_route "/index.php/reports/members-without-commitments" "Members Without Commitments" "200"
echo

# Test API Routes
echo "=== API Routes ==="
test_route "/index.php/api/member-commitments/1" "API Member 1 Commitments" "200"
test_route "/index.php/api/payment-details/1" "API Payment Details 1" "200"
test_route "/index.php/api/payment-details/1/1" "API Payment Details 1/1" "200"
echo

# Cleanup
rm -f "$COOKIE_FILE"
echo "Route testing completed!"
