-- Database Schema for Organization Accounting System

-- Create database
CREATE DATABASE IF NOT EXISTS org_accounting;
USE org_accounting;

-- Set log_bin_trust_function_creators to allow trigger creation without SUPER privilege
SET GLOBAL log_bin_trust_function_creators = 1;

-- Drop existing tables if they exist
DROP TABLE IF EXISTS collection_summary;
DROP TABLE IF EXISTS payments;
DROP TABLE IF EXISTS commitments;
DROP TABLE IF EXISTS members;

-- Members table
CREATE TABLE members (
    member_id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100),
    phone VARCHAR(20),
    address TEXT,
    join_date DATE NOT NULL,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Commitments table
CREATE TABLE commitments (
    commitment_id INT AUTO_INCREMENT PRIMARY KEY,
    member_id INT NOT NULL,
    amount DECIMAL(10, 2) NOT NULL,
    frequency ENUM('monthly', 'quarterly', 'yearly', 'one-time') DEFAULT 'monthly',
    start_date DATE NOT NULL,
    end_date DATE,  -- NULL means ongoing commitment
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (member_id) REFERENCES members(member_id) ON DELETE CASCADE
);

-- Payments table
CREATE TABLE payments (
    payment_id INT AUTO_INCREMENT PRIMARY KEY,
    member_id INT NOT NULL,
    commitment_id INT,  -- Can be NULL for payments not tied to specific commitments
    amount DECIMAL(10, 2) NOT NULL,
    payment_date DATE NOT NULL,
    receipt_number VARCHAR(50) UNIQUE,
    payment_method ENUM('cash', 'check', 'bank_transfer', 'other') DEFAULT 'cash',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (member_id) REFERENCES members(member_id) ON DELETE CASCADE,
    FOREIGN KEY (commitment_id) REFERENCES commitments(commitment_id) ON DELETE SET NULL
);

-- Collection summary table (this could be a view instead of a table)
CREATE TABLE collection_summary (
    summary_id INT AUTO_INCREMENT PRIMARY KEY,
    member_id INT NOT NULL,
    total_committed DECIMAL(10, 2) NOT NULL DEFAULT 0,
    total_paid DECIMAL(10, 2) NOT NULL DEFAULT 0,
    balance DECIMAL(10, 2) NOT NULL DEFAULT 0,
    last_payment_date DATE,
    last_payment_amount DECIMAL(10, 2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (member_id) REFERENCES members(member_id) ON DELETE CASCADE
);

-- Triggers to update collection summary when payments are made
DELIMITER //
CREATE TRIGGER after_payment_insert
AFTER INSERT ON payments
FOR EACH ROW
BEGIN
    -- Update collection summary
    UPDATE collection_summary
    SET
        total_paid = total_paid + NEW.amount,
        balance = total_committed - (total_paid + NEW.amount),
        last_payment_date = NEW.payment_date,
        last_payment_amount = NEW.amount,
        updated_at = CURRENT_TIMESTAMP
    WHERE member_id = NEW.member_id;

    -- If no summary exists, create one
    IF ROW_COUNT() = 0 THEN
        INSERT INTO collection_summary (
            member_id,
            total_committed,
            total_paid,
            balance,
            last_payment_date,
            last_payment_amount
        )
        SELECT
            NEW.member_id,
            COALESCE((SELECT SUM(amount) FROM commitments WHERE member_id = NEW.member_id), 0),
            NEW.amount,
            COALESCE((SELECT SUM(amount) FROM commitments WHERE member_id = NEW.member_id), 0) - NEW.amount,
            NEW.payment_date,
            NEW.amount;
    END IF;
END //
DELIMITER ;

-- Trigger to update collection summary when commitments are added
DELIMITER //
CREATE TRIGGER after_commitment_insert
AFTER INSERT ON commitments
FOR EACH ROW
BEGIN
    -- Update collection summary
    UPDATE collection_summary
    SET
        total_committed = total_committed + NEW.amount,
        balance = (total_committed + NEW.amount) - total_paid,
        updated_at = CURRENT_TIMESTAMP
    WHERE member_id = NEW.member_id;

    -- If no summary exists, create one
    IF ROW_COUNT() = 0 THEN
        INSERT INTO collection_summary (
            member_id,
            total_committed,
            total_paid,
            balance
        )
        VALUES (
            NEW.member_id,
            NEW.amount,
            0,
            NEW.amount
        );
    END IF;
END //
DELIMITER ;
