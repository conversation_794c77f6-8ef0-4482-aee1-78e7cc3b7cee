-- Updated Database Schema for Organization Accounting System

-- Create database
CREATE DATABASE IF NOT EXISTS org_accounting;
USE org_accounting;

-- Drop existing tables if they exist
DROP TABLE IF EXISTS collection_summary;
DROP TABLE IF EXISTS payments;
DROP TABLE IF EXISTS commitments;
DROP TABLE IF EXISTS members;

-- Members table
CREATE TABLE members (
    member_id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHA<PERSON>(100) NOT NULL,
    email VARCHAR(100),
    phone VARCHAR(20),
    address TEXT,
    join_date DATE NOT NULL,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Commitments table
CREATE TABLE commitments (
    commitment_id INT AUTO_INCREMENT PRIMARY KEY,
    member_id INT NOT NULL,
    amount DECIMAL(10, 2) NOT NULL,
    frequency ENUM('monthly', 'quarterly', 'yearly', 'one-time') DEFAULT 'monthly',
    start_date DATE NOT NULL,
    end_date DATE,  -- NULL means ongoing commitment
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (member_id) REFERENCES members(member_id) ON DELETE CASCADE
);

-- Payments table
CREATE TABLE payments (
    payment_id INT AUTO_INCREMENT PRIMARY KEY,
    member_id INT NOT NULL,
    commitment_id INT,  -- Can be NULL for payments not tied to specific commitments
    amount DECIMAL(10, 2) NOT NULL,
    payment_date DATE NOT NULL,
    receipt_number VARCHAR(50) UNIQUE,
    payment_method ENUM('cash', 'check', 'bank_transfer', 'other') DEFAULT 'cash',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (member_id) REFERENCES members(member_id) ON DELETE CASCADE,
    FOREIGN KEY (commitment_id) REFERENCES commitments(commitment_id) ON DELETE SET NULL
);

-- Collection summary table
CREATE TABLE collection_summary (
    summary_id INT AUTO_INCREMENT PRIMARY KEY,
    member_id INT NOT NULL,
    total_committed DECIMAL(10, 2) NOT NULL DEFAULT 0,
    total_paid DECIMAL(10, 2) NOT NULL DEFAULT 0,
    balance DECIMAL(10, 2) NOT NULL DEFAULT 0,
    last_payment_date DATE,
    last_payment_amount DECIMAL(10, 2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (member_id) REFERENCES members(member_id) ON DELETE CASCADE
);
