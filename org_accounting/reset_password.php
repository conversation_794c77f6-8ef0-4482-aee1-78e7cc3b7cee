<?php

// Database credentials
$hostname = 'localhost';
$database = 'org_accounting';
$username = 'root';
$password = '';

// New admin password
$newPassword = 'password123';
$adminUsername = 'boss';

// Connect to the database
try {
    $mysqli = new mysqli(
        $hostname,
        $username,
        $password,
        $database
    );

    if ($mysqli->connect_error) {
        echo "Connection failed: " . $mysqli->connect_error . "\n";
        exit;
    }
    
    echo "Connection successful!\n";
    
    // Hash the new password
    $passwordHash = password_hash($newPassword, PASSWORD_DEFAULT);
    
    // Update the admin password
    $stmt = $mysqli->prepare("UPDATE admins SET password_hash = ? WHERE username = ?");
    $stmt->bind_param("ss", $passwordHash, $adminUsername);
    
    if ($stmt->execute()) {
        echo "Password for user '$adminUsername' has been reset to '$newPassword'\n";
    } else {
        echo "Failed to reset password: " . $stmt->error . "\n";
    }
    
    $stmt->close();
    $mysqli->close();
    
} catch (Exception $e) {
    echo "Exception: " . $e->getMessage() . "\n";
}
