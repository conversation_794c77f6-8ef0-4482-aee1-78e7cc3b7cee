<?php

// Connect to the database
$db = new mysqli('localhost', 'root', '', 'org_accounting');

if ($db->connect_error) {
    die("Connection failed: " . $db->connect_error);
}

// New username
$newUsername = 'boss';
$password = 'muhammed1234#';
$email = '<EMAIL>';
$passwordHash = password_hash($password, PASSWORD_DEFAULT);

// Update the admin user
$stmt = $db->prepare("UPDATE admins SET username = ?, password_hash = ? WHERE username = 'admin'");
$stmt->bind_param("ss", $newUsername, $passwordHash);
$stmt->execute();

if ($stmt->affected_rows > 0) {
    echo "Admin username changed to 'boss' successfully.\n";
} else {
    // If no rows were affected, try to find the admin by ID
    $stmt = $db->prepare("UPDATE admins SET username = ?, password_hash = ? WHERE admin_id = 2");
    $stmt->bind_param("ss", $newUsername, $passwordHash);
    $stmt->execute();
    
    if ($stmt->affected_rows > 0) {
        echo "Admin username changed to 'boss' successfully.\n";
    } else {
        echo "No admin user found to update.\n";
        
        // Create a new admin user with username 'boss'
        $stmt = $db->prepare("INSERT INTO admins (username, password_hash, email, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())");
        $stmt->bind_param("sss", $newUsername, $passwordHash, $email);
        $stmt->execute();
        
        echo "New admin user 'boss' created successfully.\n";
    }
}

$db->close();
