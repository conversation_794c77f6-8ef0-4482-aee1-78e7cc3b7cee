{"name": "codeigniter4/appstarter", "description": "CodeIgniter4 starter app", "license": "MIT", "type": "project", "homepage": "https://codeigniter.com", "support": {"forum": "https://forum.codeigniter.com/", "source": "https://github.com/codeigniter4/CodeIgniter4", "slack": "https://codeigniterchat.slack.com"}, "require": {"php": "^8.1", "codeigniter4/framework": "^4.0"}, "require-dev": {"fakerphp/faker": "^1.9", "mikey179/vfsstream": "^1.6", "phpunit/phpunit": "^10.5.16"}, "autoload": {"psr-4": {"App\\": "app/", "Config\\": "app/Config/"}, "exclude-from-classmap": ["**/Database/Migrations/**"]}, "autoload-dev": {"psr-4": {"Tests\\Support\\": "tests/_support"}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "scripts": {"test": "phpunit"}}