# GitHub Setup Commands
# Run these commands in your terminal after creating the GitHub repository

# 1. Add the remote repository (replace YOUR_USERNAME with your GitHub username)
git remote add origin https://github.com/YOUR_USERNAME/financial-tracking-app.git

# 2. Push the code to GitHub
git branch -M main
git push -u origin main

# Alternative if you prefer SSH (make sure you have SSH keys set up)
# git remote <NAME_EMAIL>:YOUR_USERNAME/financial-tracking-app.git
# git push -u origin main

# After pushing, your repository will be available at:
# https://github.com/YOUR_USERNAME/financial-tracking-app
