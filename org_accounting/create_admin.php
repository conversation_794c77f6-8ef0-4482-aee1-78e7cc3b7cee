<?php

// Connect to the database
$db = new mysqli('localhost', 'root', '', 'org_accounting');

if ($db->connect_error) {
    die("Connection failed: " . $db->connect_error);
}

// Create admin user
$username = 'admin';
$password = 'muhammed1234#';
$email = '<EMAIL>';
$passwordHash = password_hash($password, PASSWORD_DEFAULT);

// Check if admin already exists
$stmt = $db->prepare("SELECT admin_id FROM admins WHERE username = ?");
$stmt->bind_param("s", $username);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    // Update existing admin
    $row = $result->fetch_assoc();
    $adminId = $row['admin_id'];

    $stmt = $db->prepare("UPDATE admins SET password_hash = ?, email = ? WHERE admin_id = ?");
    $stmt->bind_param("ssi", $passwordHash, $email, $adminId);
    $stmt->execute();

    echo "Admin user updated successfully.\n";
} else {
    // Create new admin
    $stmt = $db->prepare("INSERT INTO admins (username, password_hash, email, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())");
    $stmt->bind_param("sss", $username, $passwordHash, $email);
    $stmt->execute();

    echo "Admin user created successfully.\n";
}

$db->close();
