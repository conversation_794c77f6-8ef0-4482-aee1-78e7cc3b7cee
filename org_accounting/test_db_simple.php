<?php

// Database credentials
$hostname = 'localhost';
$database = 'org_accounting';
$username = 'root';
$password = '';

// Display the configuration
echo "Database Configuration:\n";
echo "Hostname: " . $hostname . "\n";
echo "Database: " . $database . "\n";
echo "Username: " . $username . "\n";
echo "Password: " . (empty($password) ? "(empty)" : "(set)") . "\n";

// Try to connect to the database
try {
    $mysqli = new mysqli(
        $hostname,
        $username,
        $password,
        $database
    );

    if ($mysqli->connect_error) {
        echo "Connection failed: " . $mysqli->connect_error . "\n";
    } else {
        echo "Connection successful!\n";
        
        // Test query to get admin users
        $result = $mysqli->query("SELECT * FROM admins");
        if ($result) {
            echo "Found " . $result->num_rows . " admin users:\n";
            while ($row = $result->fetch_assoc()) {
                echo "- " . $row['username'] . " (ID: " . $row['admin_id'] . ")\n";
            }
        } else {
            echo "Query failed: " . $mysqli->error . "\n";
        }
        
        $mysqli->close();
    }
} catch (Exception $e) {
    echo "Exception: " . $e->getMessage() . "\n";
}
