# Organization Accounting System

A financial tracking application built with CodeIgniter 4.

## Environment Setup

This application supports both development and production environments through environment-specific configuration files.

### Development Environment

1. The development environment uses the `.env` file in the project root.
2. Default settings:
   - Base URL: `http://localhost:8080/`
   - Database: `org_accounting` (local MySQL database)
   - Environment: `development` (shows detailed error messages)

### Production Environment

1. The production environment uses the `.env.production` file.
2. To deploy to production:
   - Copy `.env.production` to `.env` on your production server
   - Update the database password in the `.env` file
   - Ensure the web server has the correct permissions

```bash
# On your production server
cp .env.production .env
nano .env  # Edit to set the correct database password
```

### Important Configuration Parameters

#### Database Configuration

- `database.default.hostname`: Database server hostname
- `database.default.database`: Database name
- `database.default.username`: Database username
- `database.default.password`: Database password
- `database.default.DBDriver`: Database driver (MySQLi)
- `database.default.charset`: Character set (utf8mb4)
- `database.default.DBCollat`: Collation (utf8mb4_general_ci)

#### Application Configuration

- `app.baseURL`: Base URL with trailing slash (e.g., 'https://halqa.mazharulirfan.com/')
- `app.indexPage`: Index page (empty in production if using URL rewriting)
- `app.forceGlobalSecureRequests`: Force HTTPS (true in production)
- `app.appTimezone`: Application timezone

## Deployment Steps

1. Export your local database:
   ```bash
   mysqldump -u root org_accounting > org_accounting_backup.sql
   ```

2. Edit the SQL file to use compatible collation:
   ```bash
   sed -i 's/utf8mb4_0900_ai_ci/utf8mb4_general_ci/g' org_accounting_backup.sql
   ```

3. Create a database on your hosting:
   - Database name: `u888771413_halqa`
   - Username: `u888771413_halqa_user`
   - Set a secure password

4. Import the database:
   - Use phpMyAdmin or MySQL command line
   - Ensure character set is set to utf8mb4

5. Upload application files to your server:
   - Use FTP, SFTP, or Git
   - Ensure the web server has write permissions to the writable/ directory

6. Set up the production environment:
   - Copy `.env.production` to `.env`
   - Update the database password

7. Configure your web server:
   - Set the document root to the `public/` directory
   - Set up URL rewriting (if using clean URLs)

## Server Requirements

PHP version 8.1 or higher is required, with the following extensions installed:

- [intl](http://php.net/manual/en/intl.requirements.php)
- [mbstring](http://php.net/manual/en/mbstring.installation.php)

> [!WARNING]
> - The end of life date for PHP 7.4 was November 28, 2022.
> - The end of life date for PHP 8.0 was November 26, 2023.
> - If you are still using PHP 7.4 or 8.0, you should upgrade immediately.
> - The end of life date for PHP 8.1 will be December 31, 2025.

Additionally, make sure that the following extensions are enabled in your PHP:

- json (enabled by default - don't turn it off)
- [mysqlnd](http://php.net/manual/en/mysqlnd.install.php) if you plan to use MySQL
- [libcurl](http://php.net/manual/en/curl.requirements.php) if you plan to use the HTTP\CURLRequest library
