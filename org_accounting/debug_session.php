<?php

// Simple session configuration debug
$configPath = __DIR__ . '/app/Config/Session.php';

echo "Session Configuration Debug:\n";
echo "Config file exists: " . (file_exists($configPath) ? "YES" : "NO") . "\n";

if (file_exists($configPath)) {
    $content = file_get_contents($configPath);

    // Extract savePath line
    if (preg_match('/public string \$savePath = (.+);/', $content, $matches)) {
        echo "Raw savePath config: " . $matches[1] . "\n";

        // Try to evaluate the path
        $savePath = __DIR__ . '/../../writable/session';
        echo "Evaluated savePath: " . $savePath . "\n";
        echo "Path exists: " . (is_dir($savePath) ? "YES" : "NO") . "\n";
        echo "Path writable: " . (is_writable($savePath) ? "YES" : "NO") . "\n";

        $realPath = realpath($savePath);
        echo "Real path: " . ($realPath ?: "FAILED TO RESOLVE") . "\n";
    }
}

// Check writable directory
$writablePath = __DIR__ . '/writable/session';
echo "\nWritable session path: " . $writablePath . "\n";
echo "Writable exists: " . (is_dir($writablePath) ? "YES" : "NO") . "\n";
echo "Writable writable: " . (is_writable($writablePath) ? "YES" : "NO") . "\n";

// Test file creation
$testFile = $writablePath . '/test_' . time() . '.txt';
if (file_put_contents($testFile, 'test')) {
    echo "Test file creation: SUCCESS\n";
    unlink($testFile);
} else {
    echo "Test file creation: FAILED\n";
}
