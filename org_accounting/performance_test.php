<?php

// Performance testing script for 1000+ members
$host = 'localhost';
$dbname = 'org_accounting';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "=== PERFORMANCE TEST RESULTS ===\n\n";
} catch(PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

// Test 1: Count total members
$start = microtime(true);
$stmt = $pdo->query("SELECT COUNT(*) as total FROM members WHERE status = 'active'");
$totalMembers = $stmt->fetch()['total'];
$time1 = microtime(true) - $start;
echo "1. Count Active Members: {$totalMembers} members in " . round($time1 * 1000, 2) . "ms\n";

// Test 2: Paginated members with balance (OPTIMIZED)
$start = microtime(true);
$stmt = $pdo->prepare("
    SELECT m.*, COALESCE(cs.balance, 0) as balance, cs.last_payment_date
    FROM members m
    LEFT JOIN collection_summary cs ON m.member_id = cs.member_id
    WHERE m.status = 'active'
    ORDER BY COALESCE(cs.balance, 0) DESC
    LIMIT 50 OFFSET 0
");
$stmt->execute();
$members = $stmt->fetchAll();
$time2 = microtime(true) - $start;
echo "2. Paginated Members (50 items): " . count($members) . " members in " . round($time2 * 1000, 2) . "ms\n";

// Test 3: Search members by name (OPTIMIZED)
$start = microtime(true);
$stmt = $pdo->prepare("
    SELECT m.*, COALESCE(cs.balance, 0) as balance
    FROM members m
    LEFT JOIN collection_summary cs ON m.member_id = cs.member_id
    WHERE m.status = 'active' AND m.name LIKE ?
    ORDER BY m.name ASC
    LIMIT 50
");
$stmt->execute(['%Al%']);
$searchResults = $stmt->fetchAll();
$time3 = microtime(true) - $start;
echo "3. Search Members (name contains 'Al'): " . count($searchResults) . " results in " . round($time3 * 1000, 2) . "ms\n";

// Test 4: Get member with commitments and payments
$start = microtime(true);
$stmt = $pdo->prepare("SELECT * FROM members WHERE member_id = ? LIMIT 1");
$stmt->execute([1]);
$member = $stmt->fetch();

$stmt = $pdo->prepare("SELECT * FROM commitments WHERE member_id = ? ORDER BY created_at DESC");
$stmt->execute([1]);
$commitments = $stmt->fetchAll();

$stmt = $pdo->prepare("SELECT * FROM payments WHERE member_id = ? ORDER BY payment_date DESC");
$stmt->execute([1]);
$payments = $stmt->fetchAll();
$time4 = microtime(true) - $start;
echo "4. Member Details (ID 1): " . count($commitments) . " commitments, " . count($payments) . " payments in " . round($time4 * 1000, 2) . "ms\n";

// Test 5: Outstanding balances report
$start = microtime(true);
$stmt = $pdo->query("
    SELECT m.*, cs.balance, cs.last_payment_date
    FROM members m
    JOIN collection_summary cs ON m.member_id = cs.member_id
    WHERE m.status = 'active' AND cs.balance > 0
    ORDER BY cs.balance DESC
    LIMIT 100
");
$outstandingBalances = $stmt->fetchAll();
$time5 = microtime(true) - $start;
echo "5. Outstanding Balances (top 100): " . count($outstandingBalances) . " members in " . round($time5 * 1000, 2) . "ms\n";

// Test 6: Payment history with member details
$start = microtime(true);
$stmt = $pdo->query("
    SELECT p.*, m.name as member_name
    FROM payments p
    JOIN members m ON p.member_id = m.member_id
    ORDER BY p.payment_date DESC
    LIMIT 100
");
$paymentHistory = $stmt->fetchAll();
$time6 = microtime(true) - $start;
echo "6. Payment History (last 100): " . count($paymentHistory) . " payments in " . round($time6 * 1000, 2) . "ms\n";

// Test 7: Dashboard statistics
$start = microtime(true);
$stmt = $pdo->query("SELECT COUNT(*) as total FROM members WHERE status = 'active'");
$activeMembersCount = $stmt->fetch()['total'];

$stmt = $pdo->query("SELECT COUNT(*) as total FROM collection_summary WHERE balance > 0");
$membersWithBalance = $stmt->fetch()['total'];

$stmt = $pdo->query("SELECT SUM(balance) as total FROM collection_summary");
$totalOutstanding = $stmt->fetch()['total'];

$stmt = $pdo->query("SELECT COUNT(*) as total FROM payments WHERE payment_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)");
$recentPayments = $stmt->fetch()['total'];
$time7 = microtime(true) - $start;
echo "7. Dashboard Stats: {$activeMembersCount} active, {$membersWithBalance} with balance, {$totalOutstanding} total outstanding in " . round($time7 * 1000, 2) . "ms\n";

// Summary
$totalTime = $time1 + $time2 + $time3 + $time4 + $time5 + $time6 + $time7;
echo "\n=== PERFORMANCE SUMMARY ===\n";
echo "Total test time: " . round($totalTime * 1000, 2) . "ms\n";
echo "Average query time: " . round(($totalTime / 7) * 1000, 2) . "ms\n";

if ($totalTime < 0.1) {
    echo "✅ EXCELLENT: All queries under 100ms total\n";
} elseif ($totalTime < 0.5) {
    echo "✅ GOOD: Acceptable performance for 1000+ members\n";
} elseif ($totalTime < 1.0) {
    echo "⚠️  FAIR: May need optimization for larger datasets\n";
} else {
    echo "❌ POOR: Requires immediate optimization\n";
}

echo "\n=== RECOMMENDATIONS FOR 1000+ MEMBERS ===\n";
echo "✅ Database indexes: IMPLEMENTED\n";
echo "✅ Pagination: IMPLEMENTED (50 items per page)\n";
echo "✅ Search optimization: IMPLEMENTED\n";
echo "✅ Efficient JOINs: IMPLEMENTED\n";
echo "✅ Query result limiting: IMPLEMENTED\n";
echo "\nAdditional recommendations:\n";
echo "- Consider caching for dashboard statistics\n";
echo "- Monitor slow query log in production\n";
echo "- Consider database connection pooling\n";
echo "- Implement lazy loading for member details\n";
echo "- Add database query monitoring\n";
