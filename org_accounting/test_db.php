<?php

// Load the database configuration
require 'app/Config/Database.php';

// Create an instance of the Database class
$config = new Config\Database();

// Get the default database configuration
$default = $config->default;

// Display the configuration
echo "Database Configuration:\n";
echo "Hostname: " . $default['hostname'] . "\n";
echo "Database: " . $default['database'] . "\n";
echo "Username: " . $default['username'] . "\n";
echo "Password: " . (empty($default['password']) ? "(empty)" : "(set)") . "\n";

// Try to connect to the database
try {
    $mysqli = new mysqli(
        $default['hostname'],
        $default['username'],
        $default['password'],
        $default['database']
    );

    if ($mysqli->connect_error) {
        echo "Connection failed: " . $mysqli->connect_error . "\n";
    } else {
        echo "Connection successful!\n";
        
        // Test query to get admin users
        $result = $mysqli->query("SELECT * FROM admins");
        if ($result) {
            echo "Found " . $result->num_rows . " admin users:\n";
            while ($row = $result->fetch_assoc()) {
                echo "- " . $row['username'] . " (ID: " . $row['admin_id'] . ")\n";
            }
        } else {
            echo "Query failed: " . $mysqli->error . "\n";
        }
        
        $mysqli->close();
    }
} catch (Exception $e) {
    echo "Exception: " . $e->getMessage() . "\n";
}
