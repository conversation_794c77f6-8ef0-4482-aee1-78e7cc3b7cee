-- MySQL dump 10.13  Distrib 9.3.0, for macos15.2 (arm64)
--
-- Host: localhost    Database: org_accounting
-- ------------------------------------------------------
-- Server version	9.3.0

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `admins`
--

DROP TABLE IF EXISTS `admins`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `admins` (
  `admin_id` int unsigned NOT NULL AUTO_INCREMENT,
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `password_hash` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `last_login` datetime DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`admin_id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `admins`
--

LOCK TABLES `admins` WRITE;
/*!40000 ALTER TABLE `admins` DISABLE KEYS */;
INSERT INTO `admins` VALUES (2,'boss','$2y$12$mHaH.qx5IA2C9oTpnsLGUu8P4y6iYCz8oSuvFREQiFg.c9jK.pb6m','<EMAIL>','2025-05-25 05:50:28','2025-05-18 18:46:48','2025-05-25 05:50:28');
/*!40000 ALTER TABLE `admins` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ci_sessions`
--

DROP TABLE IF EXISTS `ci_sessions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ci_sessions` (
  `id` varchar(128) COLLATE utf8mb4_general_ci NOT NULL,
  `ip_address` varchar(45) COLLATE utf8mb4_general_ci NOT NULL,
  `timestamp` int unsigned NOT NULL DEFAULT '0',
  `data` blob NOT NULL,
  PRIMARY KEY (`id`),
  KEY `timestamp` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ci_sessions`
--

LOCK TABLES `ci_sessions` WRITE;
/*!40000 ALTER TABLE `ci_sessions` DISABLE KEYS */;
/*!40000 ALTER TABLE `ci_sessions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `collection_summary`
--

DROP TABLE IF EXISTS `collection_summary`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `collection_summary` (
  `summary_id` int NOT NULL AUTO_INCREMENT,
  `member_id` int NOT NULL,
  `total_committed` decimal(10,2) NOT NULL DEFAULT '0.00',
  `total_paid` decimal(10,2) NOT NULL DEFAULT '0.00',
  `balance` decimal(10,2) NOT NULL DEFAULT '0.00',
  `last_payment_date` date DEFAULT NULL,
  `last_payment_amount` decimal(10,2) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`summary_id`),
  KEY `member_id` (`member_id`),
  KEY `idx_summary_balance` (`balance`),
  KEY `idx_summary_member_balance` (`member_id`,`balance`),
  KEY `idx_summary_last_payment` (`last_payment_date`),
  CONSTRAINT `collection_summary_ibfk_1` FOREIGN KEY (`member_id`) REFERENCES `members` (`member_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=36 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `collection_summary`
--

LOCK TABLES `collection_summary` WRITE;
/*!40000 ALTER TABLE `collection_summary` DISABLE KEYS */;
INSERT INTO `collection_summary` VALUES (1,3,100.00,100.00,0.00,'2025-05-24',100.00,'2025-05-24 11:52:46','2025-05-24 16:46:44'),(2,2,200.00,200.00,0.00,'2025-03-17',200.00,'2025-05-24 12:26:38','2025-05-24 16:46:44'),(3,1,300.00,300.00,0.00,'2025-05-24',300.00,'2025-05-24 12:37:54','2025-05-24 16:46:44'),(4,4,2400.00,600.00,1800.00,'2025-05-24',600.00,'2025-05-24 12:37:54','2025-05-24 16:46:44'),(5,5,0.00,0.00,0.00,NULL,NULL,'2025-05-24 16:32:17','2025-05-24 16:46:44'),(6,6,5290.00,530.00,4760.00,'2025-03-15',360.00,'2025-05-24 16:32:17','2025-05-24 16:46:44'),(7,7,1810.00,100.00,1710.00,'2024-09-11',50.00,'2025-05-24 16:32:17','2025-05-24 16:46:44'),(8,8,2950.00,320.00,2630.00,'2025-05-09',160.00,'2025-05-24 16:32:17','2025-05-24 16:46:44'),(9,9,1320.00,0.00,1320.00,NULL,NULL,'2025-05-24 16:32:17','2025-05-24 16:46:44'),(10,10,110.00,330.00,-220.00,'2025-04-18',110.00,'2025-05-24 16:32:17','2025-05-24 16:46:44'),(11,11,4560.00,1350.00,3210.00,'2024-10-31',450.00,'2025-05-24 16:32:17','2025-05-24 16:46:44'),(12,12,1330.00,0.00,1330.00,NULL,NULL,'2025-05-24 16:32:17','2025-05-24 16:46:44'),(13,13,140.00,0.00,140.00,NULL,NULL,'2025-05-24 16:32:17','2025-05-24 16:46:44'),(14,14,1910.00,300.00,1610.00,'2025-05-19',70.00,'2025-05-24 16:32:17','2025-05-24 16:46:44'),(15,15,11920.00,6940.00,4980.00,'2025-05-24',4730.00,'2025-05-24 16:32:17','2025-05-24 16:59:14'),(16,16,7380.00,1290.00,6090.00,'2025-02-02',430.00,'2025-05-24 16:32:17','2025-05-24 16:46:44'),(17,17,260.00,0.00,260.00,NULL,NULL,'2025-05-24 16:32:17','2025-05-24 16:46:44'),(18,18,9150.00,380.00,8770.00,'2025-05-07',190.00,'2025-05-24 16:32:17','2025-05-24 16:46:44'),(19,19,2710.00,0.00,2710.00,NULL,NULL,'2025-05-24 16:32:17','2025-05-24 16:46:44'),(20,20,3840.00,0.00,3840.00,NULL,NULL,'2025-05-24 16:32:17','2025-05-24 16:46:44'),(21,21,10020.00,1480.00,8540.00,'2025-03-10',490.00,'2025-05-24 16:32:17','2025-05-24 16:46:44'),(22,22,4600.00,140.00,4460.00,'2025-04-14',70.00,'2025-05-24 16:32:17','2025-05-24 16:46:44'),(23,23,280.00,0.00,280.00,NULL,NULL,'2025-05-24 16:32:17','2025-05-24 16:46:44'),(24,24,5120.00,580.00,4540.00,'2025-04-10',490.00,'2025-05-24 16:32:17','2025-05-24 16:46:44'),(25,25,4780.00,1000.00,3780.00,'2024-07-25',500.00,'2025-05-24 16:32:17','2025-05-24 16:46:44'),(26,26,5070.00,380.00,4690.00,'2025-02-17',190.00,'2025-05-24 16:32:17','2025-05-24 16:46:44'),(27,27,2980.00,1460.00,1520.00,'2025-05-08',60.00,'2025-05-24 16:32:17','2025-05-24 16:46:44'),(28,28,9540.00,340.00,9200.00,'2024-11-10',170.00,'2025-05-24 16:32:17','2025-05-24 16:46:44'),(29,29,6100.00,0.00,6100.00,NULL,NULL,'2025-05-24 16:32:17','2025-05-24 16:46:44'),(30,30,6690.00,0.00,6690.00,NULL,NULL,'2025-05-24 16:32:17','2025-05-24 16:46:44'),(31,31,2790.00,0.00,2790.00,NULL,NULL,'2025-05-24 16:32:17','2025-05-24 16:46:44'),(32,32,2550.00,410.00,2140.00,'2025-01-10',100.00,'2025-05-24 16:32:17','2025-05-24 16:46:44'),(33,33,3000.00,600.00,2400.00,'2024-11-07',300.00,'2025-05-24 16:32:17','2025-05-24 16:46:44'),(34,34,5200.00,1060.00,4140.00,'2025-03-09',130.00,'2025-05-24 16:32:17','2025-05-24 16:46:44'),(35,35,400.00,0.00,400.00,NULL,NULL,'2025-05-24 16:32:17','2025-05-24 16:46:44');
/*!40000 ALTER TABLE `collection_summary` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `commitments`
--

DROP TABLE IF EXISTS `commitments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `commitments` (
  `commitment_id` int NOT NULL AUTO_INCREMENT,
  `member_id` int NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `frequency` enum('monthly','quarterly','yearly','one-time') COLLATE utf8mb4_general_ci DEFAULT 'monthly',
  `start_date` date NOT NULL,
  `end_date` date DEFAULT NULL,
  `notes` text COLLATE utf8mb4_general_ci,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`commitment_id`),
  KEY `member_id` (`member_id`),
  KEY `idx_commitments_dates` (`start_date`,`end_date`),
  KEY `idx_commitments_frequency` (`frequency`),
  KEY `idx_commitments_amount` (`amount`),
  KEY `idx_commitments_member_dates` (`member_id`,`start_date`,`end_date`),
  KEY `idx_commitments_member_frequency` (`member_id`,`frequency`),
  CONSTRAINT `commitments_ibfk_1` FOREIGN KEY (`member_id`) REFERENCES `members` (`member_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=78 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `commitments`
--

LOCK TABLES `commitments` WRITE;
/*!40000 ALTER TABLE `commitments` DISABLE KEYS */;
INSERT INTO `commitments` VALUES (4,3,100.00,'one-time','2025-05-23',NULL,'','2025-05-24 12:34:14','2025-05-24 12:34:14'),(5,2,200.00,'monthly','2024-12-01','2024-12-31','','2025-05-24 12:37:22','2025-05-24 12:37:22'),(6,4,200.00,'monthly','2025-01-01','2025-12-31','','2025-05-24 12:41:51','2025-05-24 12:41:51'),(7,1,100.00,'monthly','2024-10-01','2024-12-31','','2025-05-24 12:45:29','2025-05-24 12:45:29'),(8,6,170.00,'one-time','2024-09-01','2024-09-30',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(9,6,190.00,'monthly','2025-05-01','2025-12-31',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(10,6,360.00,'monthly','2025-01-01','2025-10-31',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(11,7,390.00,'monthly','2024-07-01','2024-10-31',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(12,7,50.00,'monthly','2024-05-01','2024-09-30',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(13,8,90.00,'one-time','2025-05-01','2025-05-31',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(14,8,100.00,'monthly','2024-09-01','2025-02-28',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(15,8,500.00,'one-time','2025-01-01','2025-01-31',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(16,8,160.00,'monthly','2025-04-01','2026-02-28',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(17,9,80.00,'monthly','2025-04-01','2025-12-31',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(18,9,150.00,'monthly','2024-11-01','2025-02-28',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(19,10,110.00,'one-time','2024-11-01','2024-11-30',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(20,11,450.00,'monthly','2024-06-01','2025-02-28',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(21,11,100.00,'one-time','2025-04-01','2025-04-30',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(22,11,410.00,'one-time','2024-10-01','2024-10-31',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(23,12,190.00,'monthly','2025-05-01','2025-11-30',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(24,13,140.00,'one-time','2025-05-01','2025-05-31',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(25,14,160.00,'monthly','2024-11-01','2025-06-30',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(26,14,70.00,'monthly','2024-12-01','2025-08-31',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(27,15,280.00,'monthly','2025-01-01','2025-08-31',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(28,15,450.00,'monthly','2024-12-01','2025-10-31',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(29,15,430.00,'monthly','2025-02-01','2025-12-31',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(30,16,420.00,'monthly','2024-11-01','2025-02-28',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(31,16,320.00,'monthly','2025-03-01','2025-06-30',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(32,16,430.00,'monthly','2024-09-01','2025-06-30',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(33,16,120.00,'one-time','2024-07-01','2024-07-31',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(34,17,260.00,'one-time','2025-04-01','2025-04-30',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(35,18,190.00,'monthly','2025-05-01','2026-04-30',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(36,18,430.00,'monthly','2025-01-01','2025-12-31',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(37,18,190.00,'monthly','2024-12-01','2025-08-31',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(38,19,90.00,'monthly','2025-03-01','2025-09-30',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(39,19,260.00,'monthly','2025-03-01','2025-10-31',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(40,20,320.00,'monthly','2024-09-01','2025-08-31',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(41,21,250.00,'monthly','2024-06-01','2025-02-28',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(42,21,370.00,'monthly','2024-09-01','2025-03-31',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(43,21,490.00,'monthly','2024-11-01','2025-05-31',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(44,21,250.00,'monthly','2024-06-01','2024-12-31',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(45,22,70.00,'monthly','2025-02-01','2025-11-30',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(46,22,300.00,'monthly','2025-03-01','2026-03-31',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(47,23,280.00,'one-time','2024-12-01','2024-12-31',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(48,24,250.00,'one-time','2025-05-01','2025-05-31',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(49,24,490.00,'monthly','2025-03-01','2025-09-30',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(50,24,450.00,'one-time','2024-10-01','2024-10-31',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(51,24,90.00,'monthly','2024-11-01','2025-09-30',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(52,25,130.00,'monthly','2024-06-01','2024-11-30',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(53,25,110.00,'monthly','2024-12-01','2025-06-30',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(54,25,210.00,'monthly','2025-04-01','2026-04-30',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(55,25,500.00,'one-time','2024-07-01','2024-07-31',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(56,26,420.00,'one-time','2024-11-01','2024-11-30',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(57,26,180.00,'monthly','2025-03-01','2025-07-31',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(58,26,190.00,'monthly','2024-12-01','2025-04-30',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(59,26,350.00,'monthly','2024-06-01','2025-01-31',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(60,27,130.00,'monthly','2024-07-01','2024-11-30',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(61,27,60.00,'monthly','2025-04-01','2026-03-31',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(62,27,310.00,'one-time','2025-04-01','2025-04-30',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(63,27,260.00,'monthly','2024-11-01','2025-03-31',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(64,28,280.00,'monthly','2025-05-01','2026-05-31',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(65,28,170.00,'monthly','2024-07-01','2025-05-31',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(66,28,310.00,'monthly','2024-10-01','2025-10-31',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(67,29,270.00,'monthly','2024-06-01','2025-05-31',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(68,29,260.00,'monthly','2024-09-01','2025-07-31',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(69,30,470.00,'monthly','2025-05-01','2025-11-30',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(70,30,340.00,'monthly','2024-12-01','2025-09-30',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(71,31,310.00,'monthly','2024-05-01','2025-01-31',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(72,32,310.00,'monthly','2024-09-01','2025-01-31',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(73,32,100.00,'monthly','2024-08-01','2025-05-31',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(74,33,300.00,'monthly','2024-07-01','2025-04-30',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(75,34,310.00,'monthly','2024-08-01','2025-08-31',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(76,34,130.00,'monthly','2025-01-01','2025-09-30',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57'),(77,35,400.00,'one-time','2024-09-01','2024-09-30',NULL,'2025-05-24 21:58:57','2025-05-24 21:58:57');
/*!40000 ALTER TABLE `commitments` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `members`
--

DROP TABLE IF EXISTS `members`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `members` (
  `member_id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) COLLATE utf8mb4_general_ci NOT NULL,
  `email` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `post_office` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `old_reference` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `phone` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `whatsapp_number` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `address` text COLLATE utf8mb4_general_ci,
  `join_date` date NOT NULL,
  `status` enum('active','inactive') COLLATE utf8mb4_general_ci DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`member_id`),
  KEY `idx_members_status` (`status`),
  KEY `idx_members_join_date` (`join_date`),
  KEY `idx_members_name` (`name`),
  KEY `idx_members_phone` (`phone`),
  KEY `idx_members_status_name` (`status`,`name`)
) ENGINE=InnoDB AUTO_INCREMENT=36 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `members`
--

LOCK TABLES `members` WRITE;
/*!40000 ALTER TABLE `members` DISABLE KEYS */;
INSERT INTO `members` VALUES (1,'Abdu Raheem KK','<EMAIL>','Karalmanna','','9947777125','9947777125','Noor Mahal, Pottathiparamb, Karalmanna P O\r\nKaralmanna\r\nCCST college road','2018-01-01','active','2025-05-17 22:16:26','2025-05-24 10:30:28'),(2,'Subeena','<EMAIL>','','','9947777124','9605447793','Noor Mahal, Pottathiparamb, Karalmanna P O\r\nKaralmanna\r\nCCST college road','2025-05-18','active','2025-05-17 23:25:30','2025-05-24 10:30:55'),(3,'Murad','<EMAIL>','Karalmanna',NULL,'996677889',NULL,'Nellaea','2025-04-08','active','2025-05-18 00:41:57','2025-05-18 14:00:36'),(4,'Riyas','','Perinthalmanna','990','9947777100','9947777100','','2025-05-24','active','2025-05-24 11:11:41','2025-05-24 11:11:41'),(5,'Omar Al-Nasser','<EMAIL>','Downtown','HIST-C-001','0537494636','0572364484',NULL,'2024-11-09','active','2025-05-24 21:58:20','2025-05-24 21:58:20'),(6,'Abdulrahman Al-Saeed','<EMAIL>','East Branch','REF005-001','0517446897','0517446897',NULL,'2024-10-26','active','2025-05-24 21:58:57','2025-05-24 21:58:57'),(7,'Zaid Khan','<EMAIL>','Downtown','OLD-A1-002','0537155671','0537155671',NULL,'2024-04-02','active','2025-05-24 21:58:57','2025-05-24 21:58:57'),(8,'Faisal Al-Mustafa','<EMAIL>','East Branch','REF002-003','0555562008','0518167587',NULL,'2024-03-03','active','2025-05-24 21:58:57','2025-05-24 21:58:57'),(9,'Tariq Al-Salman','<EMAIL>','Commercial District','OLD-B2-004','0567188437','0567188437',NULL,'2025-04-08','active','2025-05-24 21:58:57','2025-05-24 21:58:57'),(10,'Fahad Al-Faisal','<EMAIL>','Industrial Area','PREV-002-005','0540420476','0579349018',NULL,'2024-03-14','active','2025-05-24 21:58:57','2025-05-24 21:58:57'),(11,'Faisal Al-Salman','<EMAIL>','Suburb Office','REF005-006','0538327220','0543727340',NULL,'2023-10-13','active','2025-05-24 21:58:57','2025-05-24 21:58:57'),(12,'Amjad Al-Adnan','<EMAIL>','Suburb Office','LEGACY-03-007','0570176974','0570176974',NULL,'2023-08-09','active','2025-05-24 21:58:57','2025-05-24 21:58:57'),(13,'Abdullah Al-Mahmoud','<EMAIL>','West Branch','LEGACY-03-008','0592620160','0592620160',NULL,'2023-09-27','active','2025-05-24 21:58:57','2025-05-24 21:58:57'),(14,'Ibrahim Al-Saud','<EMAIL>','Commercial District','PREV-001-009','0547398070','0515678096',NULL,'2025-03-17','active','2025-05-24 21:58:57','2025-05-24 21:58:57'),(15,'Adnan Al-Ali','<EMAIL>','Suburb Office','OLD-A1-010','0568405834','0568405834',NULL,'2024-09-01','active','2025-05-24 21:58:57','2025-05-24 21:58:57'),(16,'Omar Al-Mustafa','<EMAIL>','Main Post Office','OLD-E5-011','0527775406','0527775406',NULL,'2023-07-03','active','2025-05-24 21:58:57','2025-05-24 21:58:57'),(17,'Saud Al-Bilal','<EMAIL>','Commercial District','REF005-012','0561548613','0561548613',NULL,'2023-08-06','active','2025-05-24 21:58:57','2025-05-24 21:58:57'),(18,'Bilal Al-Waleed','<EMAIL>','Industrial Area','LEGACY-02-013','0515351788','0522375982',NULL,'2023-08-22','active','2025-05-24 21:58:57','2025-05-24 21:58:57'),(19,'Waleed Al-Adnan','<EMAIL>','West Branch','OLD-C3-014','0511853916','0565829914',NULL,'2025-02-14','active','2025-05-24 21:58:57','2025-05-24 21:58:57'),(20,'Ibrahim Al-Ibrahim','<EMAIL>','Commercial District','LEGACY-03-015','0529224145','0529224145',NULL,'2023-12-19','active','2025-05-24 21:58:57','2025-05-24 21:58:57'),(21,'Bilal Al-Jamal','<EMAIL>','Central Post Office','PREV-001-016','0528141858','0523298126',NULL,'2023-08-28','active','2025-05-24 21:58:57','2025-05-24 21:58:57'),(22,'Fahad Al-Mustafa','<EMAIL>','Commercial District','HIST-A-017','0546804117','0513423634',NULL,'2024-05-19','active','2025-05-24 21:58:57','2025-05-24 21:58:57'),(23,'Ali Al-Omar','<EMAIL>','Central Post Office','HIST-A-018','0527527389','0527527389',NULL,'2024-07-15','active','2025-05-24 21:58:57','2025-05-24 21:58:57'),(24,'Rashid Al-Rashid','<EMAIL>','Residential Area','REF003-019','0513240716','0556112667',NULL,'2025-03-07','active','2025-05-24 21:58:57','2025-05-24 21:58:57'),(25,'Ismail Al-Waleed','<EMAIL>','South Branch','LEGACY-02-020','0595811659','0570567329',NULL,'2024-12-12','active','2025-05-24 21:58:57','2025-05-24 21:58:57'),(26,'Faisal Al-Hamza','<EMAIL>','Downtown','OLD-C3-021','0559259386','0559259386',NULL,'2023-10-21','active','2025-05-24 21:58:57','2025-05-24 21:58:57'),(27,'Salman Al-Salman','<EMAIL>','South Branch','REF004-022','0582001879','0568616961',NULL,'2023-11-17','active','2025-05-24 21:58:57','2025-05-24 21:58:57'),(28,'Saud Al-Ahmad','<EMAIL>','Downtown','LEGACY-01-023','0592485468','0554246003',NULL,'2023-06-11','active','2025-05-24 21:58:57','2025-05-24 21:58:57'),(29,'Salman Al-Bilal','<EMAIL>','Residential Area','OLD-A1-024','0541587438','0523101499',NULL,'2024-09-09','active','2025-05-24 21:58:57','2025-05-24 21:58:57'),(30,'Ibrahim Al-Hassan','<EMAIL>','Downtown','HIST-B-025','0549461895','0582341569',NULL,'2024-05-26','active','2025-05-24 21:58:57','2025-05-24 21:58:57'),(31,'Badr Al-Ibrahim','<EMAIL>','Suburb Office','LEGACY-03-026','0575605153','0575605153',NULL,'2024-08-21','active','2025-05-24 21:58:57','2025-05-24 21:58:57'),(32,'Fahad Al-Majid','<EMAIL>','Commercial District','REF004-027','0576162670','0570066376',NULL,'2023-06-24','active','2025-05-24 21:58:57','2025-05-24 21:58:57'),(33,'Faisal Khan','<EMAIL>','Industrial Area','REF005-028','0526623146','0526917902',NULL,'2024-06-02','active','2025-05-24 21:58:57','2025-05-24 21:58:57'),(34,'Hassan Al-Ahmad','<EMAIL>','South Branch','OLD-A1-029','0516419744','0582425147',NULL,'2024-08-20','active','2025-05-24 21:58:57','2025-05-24 21:58:57'),(35,'Ismail Al-Abdullah','<EMAIL>','Central Post Office','REF002-030','0529592026','0538836358',NULL,'2023-12-26','active','2025-05-24 21:58:57','2025-05-24 21:58:57');
/*!40000 ALTER TABLE `members` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `migrations`
--

DROP TABLE IF EXISTS `migrations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `migrations` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `version` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `class` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `group` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `namespace` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `time` int NOT NULL,
  `batch` int unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `migrations`
--

LOCK TABLES `migrations` WRITE;
/*!40000 ALTER TABLE `migrations` DISABLE KEYS */;
INSERT INTO `migrations` VALUES (1,'2025-05-18-130339','App\\Database\\Migrations\\CreateAdminsTable','default','App',1747573846,1),(2,'2025-05-18-132730','App\\Database\\Migrations\\AddPaymentPeriodsToPayments','default','App',1747574959,2),(3,'2025-05-24-202854','App\\Database\\Migrations\\CreateSessionsTable','default','App',1748118586,3);
/*!40000 ALTER TABLE `migrations` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `payments`
--

DROP TABLE IF EXISTS `payments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `payments` (
  `payment_id` int NOT NULL AUTO_INCREMENT,
  `member_id` int NOT NULL,
  `commitment_id` int DEFAULT NULL,
  `amount` decimal(10,2) NOT NULL,
  `payment_date` date NOT NULL,
  `receipt_number` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `receipt_book_number` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `payment_method` enum('cash','check','bank_transfer','other') COLLATE utf8mb4_general_ci DEFAULT 'cash',
  `notes` text COLLATE utf8mb4_general_ci,
  `payment_periods` text COLLATE utf8mb4_general_ci,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`payment_id`),
  UNIQUE KEY `receipt_number` (`receipt_number`),
  KEY `member_id` (`member_id`),
  KEY `commitment_id` (`commitment_id`),
  KEY `idx_payments_date` (`payment_date`),
  KEY `idx_payments_member_date` (`member_id`,`payment_date`),
  KEY `idx_payments_commitment_date` (`commitment_id`,`payment_date`),
  KEY `idx_payments_periods` (`payment_periods`(50)),
  KEY `idx_payments_date_member` (`payment_date`,`member_id`),
  CONSTRAINT `payments_ibfk_1` FOREIGN KEY (`member_id`) REFERENCES `members` (`member_id`) ON DELETE CASCADE,
  CONSTRAINT `payments_ibfk_2` FOREIGN KEY (`commitment_id`) REFERENCES `commitments` (`commitment_id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=59 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `payments`
--

LOCK TABLES `payments` WRITE;
/*!40000 ALTER TABLE `payments` DISABLE KEYS */;
INSERT INTO `payments` VALUES (1,3,4,100.00,'2025-05-24','RCPT2025050001','1','cash','',NULL,'2025-05-24 12:35:13','2025-05-24 12:35:13'),(2,1,7,300.00,'2025-05-24','RCPT2025050002','1','bank_transfer','Payment for: October 2024, November 2024, December 2024','[\"2024-10\",\"2024-11\",\"2024-12\"]','2025-05-24 13:25:21','2025-05-24 13:25:21'),(3,2,5,200.00,'2025-03-17','RCPT2025050003','1','cash','Payment for: December 2024','[\"2024-12\"]','2025-05-24 14:12:13','2025-05-24 14:12:13'),(4,4,6,600.00,'2025-05-24','RCPT2025050004','3','other','Payment for: January 2025, February 2025, March 2025','[\"2025-01\",\"2025-02\",\"2025-03\"]','2025-05-24 14:13:50','2025-05-24 14:13:50'),(5,6,8,170.00,'2025-02-23','1471','BK05','cash',NULL,'2025-03','2025-05-24 21:58:57','2025-05-24 21:58:57'),(6,6,10,360.00,'2025-03-15','1146','BK08','cash',NULL,'2025-01','2025-05-24 21:58:57','2025-05-24 21:58:57'),(7,7,12,50.00,'2024-09-11','3437','BK19','cash',NULL,'2024-09','2025-05-24 21:58:57','2025-05-24 21:58:57'),(8,7,12,50.00,'2024-05-17','3120','BK12','cash',NULL,'2024-06','2025-05-24 21:58:57','2025-05-24 21:58:57'),(9,8,16,160.00,'2025-04-18','9022','BK19','cash',NULL,'2025-06','2025-05-24 21:58:57','2025-05-24 21:58:57'),(10,8,16,160.00,'2025-05-09','2214','BK06','cash',NULL,'2025-06','2025-05-24 21:58:57','2025-05-24 21:58:57'),(11,10,19,110.00,'2025-01-17','2465','BK04','cash',NULL,'2025-05','2025-05-24 21:58:57','2025-05-24 21:58:57'),(12,10,19,110.00,'2025-04-18','3375','BK12','cash',NULL,'2025-01','2025-05-24 21:58:57','2025-05-24 21:58:57'),(13,10,19,110.00,'2025-02-09','5595','BK17','cash',NULL,'2025-05','2025-05-24 21:58:57','2025-05-24 21:58:57'),(14,11,20,450.00,'2024-08-05','8510','BK04','cash',NULL,'2024-06','2025-05-24 21:58:57','2025-05-24 21:58:57'),(15,11,20,450.00,'2024-10-31','4707','BK07','cash',NULL,'2024-07','2025-05-24 21:58:57','2025-05-24 21:58:57'),(16,11,20,450.00,'2024-07-27','2389','BK01','cash',NULL,'2024-06','2025-05-24 21:58:57','2025-05-24 21:58:57'),(17,14,25,160.00,'2025-02-19','3030','BK18','cash',NULL,'2025-01','2025-05-24 21:58:57','2025-05-24 21:58:57'),(18,14,26,70.00,'2025-05-19','8961','BK02','cash',NULL,'2025-03','2025-05-24 21:58:57','2025-05-24 21:58:57'),(19,14,26,70.00,'2025-03-26','5163','BK14','cash',NULL,'2025-01','2025-05-24 21:58:57','2025-05-24 21:58:57'),(20,15,28,450.00,'2025-03-02','7461','BK06','cash',NULL,'2025-02','2025-05-24 21:58:57','2025-05-24 21:58:57'),(21,15,28,450.00,'2024-12-19','9082','BK18','cash',NULL,'2025-05','2025-05-24 21:58:57','2025-05-24 21:58:57'),(22,15,28,450.00,'2025-02-26','7795','BK06','cash',NULL,'2025-03','2025-05-24 21:58:57','2025-05-24 21:58:57'),(23,15,29,430.00,'2025-03-09','4064','BK12','cash',NULL,'2025-07','2025-05-24 21:58:57','2025-05-24 21:58:57'),(24,15,29,430.00,'2025-05-18','7516','BK18','cash',NULL,'2025-06','2025-05-24 21:58:57','2025-05-24 21:58:57'),(25,16,32,430.00,'2025-02-02','6839','BK02','cash',NULL,'2024-10','2025-05-24 21:58:57','2025-05-24 21:58:57'),(26,16,32,430.00,'2024-12-16','4791','BK12','cash',NULL,'2025-01','2025-05-24 21:58:57','2025-05-24 21:58:57'),(27,16,32,430.00,'2024-11-21','6529','BK06','cash',NULL,'2024-11','2025-05-24 21:58:57','2025-05-24 21:58:57'),(28,18,37,190.00,'2025-01-08','2119','BK10','cash',NULL,'2025-01','2025-05-24 21:58:57','2025-05-24 21:58:57'),(29,18,37,190.00,'2025-05-07','5602','BK19','cash',NULL,'2025-06','2025-05-24 21:58:57','2025-05-24 21:58:57'),(30,21,41,250.00,'2024-10-17','1393','BK13','cash',NULL,'2024-09','2025-05-24 21:58:57','2025-05-24 21:58:57'),(31,21,41,250.00,'2024-07-13','6107','BK09','cash',NULL,'2024-08','2025-05-24 21:58:57','2025-05-24 21:58:57'),(32,21,43,490.00,'2025-03-10','4597','BK17','cash',NULL,'2024-12','2025-05-24 21:58:57','2025-05-24 21:58:57'),(33,21,43,490.00,'2025-02-05','3023','BK14','cash',NULL,'2025-03','2025-05-24 21:58:57','2025-05-24 21:58:57'),(34,22,45,70.00,'2025-04-14','2112','BK01','cash',NULL,'2025-03','2025-05-24 21:58:57','2025-05-24 21:58:57'),(35,22,45,70.00,'2025-03-07','4239','BK14','cash',NULL,'2025-08','2025-05-24 21:58:57','2025-05-24 21:58:57'),(36,24,49,490.00,'2025-04-10','4431','BK04','cash',NULL,'2025-09','2025-05-24 21:58:57','2025-05-24 21:58:57'),(37,24,51,90.00,'2024-12-11','6631','BK11','cash',NULL,'2024-11','2025-05-24 21:58:57','2025-05-24 21:58:57'),(38,25,55,500.00,'2024-07-02','7972','BK10','cash',NULL,'2024-08','2025-05-24 21:58:57','2025-05-24 21:58:57'),(39,25,55,500.00,'2024-07-25','4265','BK14','cash',NULL,'2025-01','2025-05-24 21:58:57','2025-05-24 21:58:57'),(40,26,58,190.00,'2025-02-17','2222','BK01','cash',NULL,'2025-02','2025-05-24 21:58:57','2025-05-24 21:58:57'),(41,26,58,190.00,'2025-01-07','3615','BK07','cash',NULL,'2024-12','2025-05-24 21:58:57','2025-05-24 21:58:57'),(42,27,61,60.00,'2025-05-08','7808','BK02','cash',NULL,'2025-04','2025-05-24 21:58:57','2025-05-24 21:58:57'),(43,27,62,310.00,'2025-04-20','6230','BK07','cash',NULL,'2025-08','2025-05-24 21:58:57','2025-05-24 21:58:57'),(44,27,62,310.00,'2025-04-03','3341','BK02','cash',NULL,'2025-10','2025-05-24 21:58:57','2025-05-24 21:58:57'),(45,27,63,260.00,'2025-04-27','8166','BK12','cash',NULL,'2025-04','2025-05-24 21:58:57','2025-05-24 21:58:57'),(46,27,63,260.00,'2025-01-31','6908','BK10','cash',NULL,'2025-03','2025-05-24 21:58:57','2025-05-24 21:58:57'),(47,27,63,260.00,'2025-02-13','6206','BK12','cash',NULL,'2025-05','2025-05-24 21:58:57','2025-05-24 21:58:57'),(48,28,65,170.00,'2024-11-10','2381','BK03','cash',NULL,'2024-08','2025-05-24 21:58:57','2025-05-24 21:58:57'),(49,28,65,170.00,'2024-09-05','9027','BK06','cash',NULL,'2024-11','2025-05-24 21:58:57','2025-05-24 21:58:57'),(50,32,72,310.00,'2024-10-08','1704','BK02','cash',NULL,'2025-01','2025-05-24 21:58:57','2025-05-24 21:58:57'),(51,32,73,100.00,'2025-01-10','3527','BK04','cash',NULL,'2025-02','2025-05-24 21:58:57','2025-05-24 21:58:57'),(52,33,74,300.00,'2024-11-07','9036','BK16','cash',NULL,'2025-01','2025-05-24 21:58:57','2025-05-24 21:58:57'),(53,33,74,300.00,'2024-07-30','2622','BK01','cash',NULL,'2025-01','2025-05-24 21:58:57','2025-05-24 21:58:57'),(54,34,75,310.00,'2024-09-07','7546','BK11','cash',NULL,'2024-09','2025-05-24 21:58:57','2025-05-24 21:58:57'),(55,34,75,310.00,'2025-01-16','8382','BK12','cash',NULL,'2024-09','2025-05-24 21:58:57','2025-05-24 21:58:57'),(56,34,75,310.00,'2024-10-05','1424','BK16','cash',NULL,'2024-08','2025-05-24 21:58:57','2025-05-24 21:58:57'),(57,34,76,130.00,'2025-03-09','2284','BK07','cash',NULL,'2025-03','2025-05-24 21:58:57','2025-05-24 21:58:57'),(58,15,29,4730.00,'2025-05-24','RCPT2025050005','4','check','Payment for: February 2025, March 2025, April 2025, May 2025, June 2025, July 2025, August 2025, September 2025, October 2025, November 2025, December 2025','[\"2025-02\",\"2025-03\",\"2025-04\",\"2025-05\",\"2025-06\",\"2025-07\",\"2025-08\",\"2025-09\",\"2025-10\",\"2025-11\",\"2025-12\"]','2025-05-24 16:59:14','2025-05-24 16:59:14');
/*!40000 ALTER TABLE `payments` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Dumping routines for database 'org_accounting'
--
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-05-28  7:18:33
