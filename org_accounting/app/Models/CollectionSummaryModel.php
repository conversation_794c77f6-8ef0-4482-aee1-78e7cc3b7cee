<?php

namespace App\Models;

use CodeIgniter\Model;

class CollectionSummaryModel extends Model
{
    protected $table = 'collection_summary';
    protected $primaryKey = 'summary_id';

    protected $useAutoIncrement = true;
    protected $returnType = 'array';

    protected $allowedFields = [
        'member_id',
        'total_committed',
        'total_paid',
        'balance',
        'last_payment_date',
        'last_payment_amount'
    ];

    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    /**
     * Get summary for a specific member
     *
     * @param int $memberId
     * @return array
     */
    public function getMemberSummary($memberId)
    {
        return $this->where('member_id', $memberId)->first();
    }

    /**
     * Get all summaries with member details
     *
     * @param bool $excludeZeroBalances Whether to exclude members with zero balances
     * @return array
     */
    public function getAllSummariesWithMemberDetails($excludeZeroBalances = false)
    {
        $db = \Config\Database::connect();

        $builder = $db->table('collection_summary cs')
            ->select('cs.*, m.name as member_name, m.status')
            ->join('members m', 'm.member_id = cs.member_id');

        if ($excludeZeroBalances) {
            $builder->where('cs.balance > 0');
        }

        $builder->orderBy('m.name', 'ASC');
        $query = $builder->get();

        return $query->getResultArray();
    }

    /**
     * Recalculate summary for a member
     * This can be used to fix any discrepancies in the summary table
     *
     * @param int $memberId
     * @return bool
     */
    public function recalculateSummary($memberId)
    {
        $commitmentModel = new CommitmentModel();
        $paymentModel = new PaymentModel();

        $totalCommitted = $commitmentModel->calculateTotalCommitment($memberId);
        $totalPaid = $paymentModel->calculateTotalPayments($memberId);
        $balance = $totalCommitted - $totalPaid;

        // Get last payment details
        $lastPayment = $paymentModel->where('member_id', $memberId)
            ->orderBy('payment_date', 'DESC')
            ->first();

        $lastPaymentDate = $lastPayment ? $lastPayment['payment_date'] : null;
        $lastPaymentAmount = $lastPayment ? $lastPayment['amount'] : null;

        // Check if summary exists
        $summary = $this->where('member_id', $memberId)->first();

        if ($summary) {
            // Update existing summary
            return $this->update($summary['summary_id'], [
                'total_committed' => $totalCommitted,
                'total_paid' => $totalPaid,
                'balance' => $balance,
                'last_payment_date' => $lastPaymentDate,
                'last_payment_amount' => $lastPaymentAmount
            ]);
        } else {
            // Create new summary
            return $this->insert([
                'member_id' => $memberId,
                'total_committed' => $totalCommitted,
                'total_paid' => $totalPaid,
                'balance' => $balance,
                'last_payment_date' => $lastPaymentDate,
                'last_payment_amount' => $lastPaymentAmount
            ]);
        }
    }

    /**
     * Get members with outstanding balances
     *
     * @return array
     */
    public function getMembersWithOutstandingBalances()
    {
        $db = \Config\Database::connect();

        $query = $db->table('collection_summary cs')
            ->select('cs.*, m.name as member_name, m.status, m.phone')
            ->join('members m', 'm.member_id = cs.member_id')
            ->where('cs.balance > 0')
            ->where('m.status', 'active')
            ->orderBy('cs.balance', 'DESC')
            ->get();

        return $query->getResultArray();
    }
}
