<?php

namespace App\Models;

use CodeIgniter\Model;

class CollectionSummaryModel extends Model
{
    protected $table = 'collection_summary';
    protected $primaryKey = 'summary_id';

    protected $useAutoIncrement = true;
    protected $returnType = 'array';

    protected $allowedFields = [
        'member_id',
        'total_committed',
        'total_paid',
        'balance',
        'last_payment_date',
        'last_payment_amount'
    ];

    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    /**
     * Get summary for a specific member
     *
     * @param int $memberId
     * @return array
     */
    public function getMemberSummary($memberId)
    {
        return $this->where('member_id', $memberId)->first();
    }

    /**
     * Get all summaries with member details
     *
     * @param bool $excludeZeroBalances Whether to exclude members with zero balances
     * @param bool $excludeZeroCommitments Whether to exclude members with zero commitments
     * @return array
     */
    public function getAllSummariesWithMemberDetails($excludeZeroBalances = false, $excludeZeroCommitments = false)
    {
        $db = \Config\Database::connect();

        $builder = $db->table('collection_summary cs')
            ->select('cs.*, m.name as member_name, m.status')
            ->join('members m', 'm.member_id = cs.member_id');

        if ($excludeZeroBalances) {
            $builder->where('cs.balance > 0');
        } elseif ($excludeZeroCommitments) {
            $builder->where('cs.total_committed > 0');
        }

        $builder->orderBy('m.name', 'ASC');
        $query = $builder->get();

        return $query->getResultArray();
    }

    /**
     * Get all summaries with member details and date filtering
     * Filters by commitment months and payment months, not creation/payment dates
     *
     * @param bool $excludeZeroBalances Whether to exclude members with zero balances
     * @param bool $excludeZeroCommitments Whether to exclude members with zero commitments
     * @param string $fromDate Start date (Y-m-d format)
     * @param string $toDate End date (Y-m-d format)
     * @return array
     */
    public function getAllSummariesWithMemberDetailsAndDateFilter($excludeZeroBalances = false, $excludeZeroCommitments = false, $fromDate = null, $toDate = null)
    {
        $db = \Config\Database::connect();

        // Convert dates to Y-m format for month comparison
        $fromMonth = date('Y-m', strtotime($fromDate));
        $toMonth = date('Y-m', strtotime($toDate));

        // Get all active members
        $members = $db->table('members')
            ->select('member_id, name as member_name, status')
            ->where('status', 'active')
            ->orderBy('name', 'ASC')
            ->get()
            ->getResultArray();

        $results = [];

        foreach ($members as $member) {
            $memberId = $member['member_id'];

            // Calculate committed amount for the date range
            $totalCommitted = $this->calculateCommittedAmountForDateRange($memberId, $fromMonth, $toMonth);

            // Calculate paid amount for the date range
            $totalPaid = $this->calculatePaidAmountForDateRange($memberId, $fromMonth, $toMonth);

            // Get last payment info (overall, not filtered by date)
            $lastPayment = $db->table('payments')
                ->select('payment_date, amount')
                ->where('member_id', $memberId)
                ->orderBy('payment_date', 'DESC')
                ->get()
                ->getRowArray();

            $balance = $totalCommitted - $totalPaid;

            // Apply filtering based on selected option
            if ($excludeZeroBalances && $balance <= 0) {
                continue;
            } elseif ($excludeZeroCommitments && $totalCommitted <= 0) {
                continue;
            }

            $results[] = [
                'member_id' => $memberId,
                'member_name' => $member['member_name'],
                'status' => $member['status'],
                'total_committed' => $totalCommitted,
                'total_paid' => $totalPaid,
                'balance' => $balance,
                'last_payment_date' => $lastPayment['payment_date'] ?? null,
                'last_payment_amount' => $lastPayment['amount'] ?? null
            ];
        }

        return $results;
    }

    /**
     * Calculate committed amount for a member within a date range
     *
     * @param int $memberId
     * @param string $fromMonth Y-m format
     * @param string $toMonth Y-m format
     * @return float
     */
    private function calculateCommittedAmountForDateRange($memberId, $fromMonth, $toMonth)
    {
        $db = \Config\Database::connect();

        $commitments = $db->table('commitments')
            ->where('member_id', $memberId)
            ->get()
            ->getResultArray();

        $totalCommitted = 0;

        foreach ($commitments as $commitment) {
            $amount = $commitment['amount'];
            $frequency = $commitment['frequency'];
            $startDate = $commitment['start_date'];
            $endDate = $commitment['end_date'];

            if ($frequency === 'one-time') {
                // For one-time commitments, check if start_date falls within the range
                $commitmentMonth = date('Y-m', strtotime($startDate));
                if ($commitmentMonth >= $fromMonth && $commitmentMonth <= $toMonth) {
                    $totalCommitted += $amount;
                }
            } else if ($frequency === 'monthly') {
                // For monthly commitments, calculate months within the range
                $commitmentStart = date('Y-m', strtotime($startDate));
                $commitmentEnd = $endDate ? date('Y-m', strtotime($endDate)) : $toMonth;

                // Find overlap between commitment period and filter period
                $overlapStart = max($commitmentStart, $fromMonth);
                $overlapEnd = min($commitmentEnd, $toMonth);

                if ($overlapStart <= $overlapEnd) {
                    // Calculate number of months in overlap
                    $startTime = strtotime($overlapStart . '-01');
                    $endTime = strtotime($overlapEnd . '-01');
                    $months = 0;

                    while ($startTime <= $endTime) {
                        $months++;
                        $startTime = strtotime('+1 month', $startTime);
                    }

                    $totalCommitted += $amount * $months;
                }
            }
        }

        return $totalCommitted;
    }

    /**
     * Calculate paid amount for a member within a date range (by payment periods)
     *
     * @param int $memberId
     * @param string $fromMonth Y-m format
     * @param string $toMonth Y-m format
     * @return float
     */
    private function calculatePaidAmountForDateRange($memberId, $fromMonth, $toMonth)
    {
        $db = \Config\Database::connect();

        $payments = $db->table('payments')
            ->where('member_id', $memberId)
            ->get()
            ->getResultArray();

        $totalPaid = 0;

        foreach ($payments as $payment) {
            $paymentPeriods = $payment['payment_periods'];
            $paymentAmount = $payment['amount'];

            if (empty($paymentPeriods) || $paymentPeriods === 'NULL') {
                // For legacy payments without payment_periods, we'll estimate based on payment date
                // This is not ideal but provides some data for older payments
                $paymentDate = $payment['payment_date'];
                $paymentMonth = date('Y-m', strtotime($paymentDate));

                // Only include if payment date falls within the filter range
                if ($paymentMonth >= $fromMonth && $paymentMonth <= $toMonth) {
                    $totalPaid += $paymentAmount;
                }
                continue;
            }

            // Parse payment periods - handle both JSON array and comma-separated formats
            $periods = [];

            // Check if it's JSON format (starts with [ and ends with ])
            if (strpos($paymentPeriods, '[') === 0 && strpos($paymentPeriods, ']') !== false) {
                $periodsArray = json_decode($paymentPeriods, true);
                if (is_array($periodsArray)) {
                    $periods = $periodsArray;
                }
            } else {
                // Handle comma-separated format
                $periods = explode(',', $paymentPeriods);
            }

            if (empty($periods)) {
                continue;
            }

            $validPeriods = 0;
            $totalPeriods = count($periods);

            foreach ($periods as $period) {
                $period = trim($period, ' "\''); // Remove quotes and whitespace
                if (strlen($period) >= 7) { // Y-m format
                    $periodMonth = substr($period, 0, 7); // Extract Y-m part
                    if ($periodMonth >= $fromMonth && $periodMonth <= $toMonth) {
                        $validPeriods++;
                    }
                }
            }

            // Calculate proportional amount for periods within range
            if ($validPeriods > 0 && $totalPeriods > 0) {
                $totalPaid += ($paymentAmount * $validPeriods) / $totalPeriods;
            }
        }

        return $totalPaid;
    }

    /**
     * Recalculate summary for a member
     * This can be used to fix any discrepancies in the summary table
     *
     * @param int $memberId
     * @return bool
     */
    public function recalculateSummary($memberId)
    {
        $commitmentModel = new CommitmentModel();
        $paymentModel = new PaymentModel();

        $totalCommitted = $commitmentModel->calculateTotalCommitment($memberId);
        $totalPaid = $paymentModel->calculateTotalPayments($memberId);
        $balance = $totalCommitted - $totalPaid;

        // Get last payment details
        $lastPayment = $paymentModel->where('member_id', $memberId)
            ->orderBy('payment_date', 'DESC')
            ->first();

        $lastPaymentDate = $lastPayment ? $lastPayment['payment_date'] : null;
        $lastPaymentAmount = $lastPayment ? $lastPayment['amount'] : null;

        // Check if summary exists
        $summary = $this->where('member_id', $memberId)->first();

        if ($summary) {
            // Update existing summary
            return $this->update($summary['summary_id'], [
                'total_committed' => $totalCommitted,
                'total_paid' => $totalPaid,
                'balance' => $balance,
                'last_payment_date' => $lastPaymentDate,
                'last_payment_amount' => $lastPaymentAmount
            ]);
        } else {
            // Create new summary
            return $this->insert([
                'member_id' => $memberId,
                'total_committed' => $totalCommitted,
                'total_paid' => $totalPaid,
                'balance' => $balance,
                'last_payment_date' => $lastPaymentDate,
                'last_payment_amount' => $lastPaymentAmount
            ]);
        }
    }

    /**
     * Get members with outstanding balances
     *
     * @return array
     */
    public function getMembersWithOutstandingBalances()
    {
        $db = \Config\Database::connect();

        $query = $db->table('collection_summary cs')
            ->select('cs.*, m.name as member_name, m.status, m.phone')
            ->join('members m', 'm.member_id = cs.member_id')
            ->where('cs.balance > 0')
            ->where('m.status', 'active')
            ->orderBy('cs.balance', 'DESC')
            ->get();

        return $query->getResultArray();
    }
}
