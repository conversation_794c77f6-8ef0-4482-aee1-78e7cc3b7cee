<?php

namespace App\Models;

use CodeIgniter\Model;

class MemberModel extends Model
{
    protected $table = 'members';
    protected $primaryKey = 'member_id';

    protected $useAutoIncrement = true;
    protected $returnType = 'array';

    protected $allowedFields = [
        'name',
        'email',
        'post_office',
        'old_reference',
        'phone',
        'whatsapp_number',
        'address',
        'join_date',
        'status'
    ];

    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    protected $validationRules = [
        'name' => 'required|min_length[3]|max_length[100]',
        'email' => 'permit_empty|valid_email|max_length[100]',
        'post_office' => 'permit_empty|max_length[100]',
        'old_reference' => 'permit_empty|max_length[100]',
        'phone' => 'permit_empty|max_length[20]',
        'whatsapp_number' => 'permit_empty|max_length[20]',
        'join_date' => 'required|valid_date',
        'status' => 'permit_empty|in_list[active,inactive]'
    ];

    protected $validationMessages = [
        'name' => [
            'required' => 'Name is required',
            'min_length' => 'Name must be at least 3 characters long',
            'max_length' => 'Name cannot exceed 100 characters'
        ],
        'email' => [
            'valid_email' => 'Please check the Email field. It does not appear to be valid',
            'max_length' => 'Email cannot exceed 100 characters'
        ],
        'post_office' => [
            'max_length' => 'Post Office cannot exceed 100 characters'
        ],
        'old_reference' => [
            'max_length' => 'Old Reference cannot exceed 100 characters'
        ],
        'whatsapp_number' => [
            'max_length' => 'WhatsApp number cannot exceed 20 characters'
        ],
        'join_date' => [
            'required' => 'Join date is required',
            'valid_date' => 'Join date must be a valid date'
        ],
        'status' => [
            'in_list' => 'Status must be either active or inactive'
        ]
    ];

    protected $skipValidation = false;

    /**
     * Get member with their commitments and payments
     *
     * @param int $memberId
     * @return array
     */
    public function getMemberWithDetails($memberId)
    {
        $member = $this->find($memberId);

        if (!$member) {
            return null;
        }

        $commitmentModel = new CommitmentModel();
        $paymentModel = new PaymentModel();
        $summaryModel = new CollectionSummaryModel();

        $member['commitments'] = $commitmentModel->where('member_id', $memberId)
            ->orderBy('created_at', 'DESC')
            ->findAll();
        $member['payments'] = $paymentModel->where('member_id', $memberId)
            ->orderBy('payment_date', 'DESC')
            ->findAll();
        $member['summary'] = $summaryModel->where('member_id', $memberId)->first();

        return $member;
    }

    /**
     * Get all active members
     *
     * @return array
     */
    public function getActiveMembers()
    {
        return $this->where('status', 'active')->findAll();
    }

    /**
     * Get members with outstanding balances
     *
     * @return array
     */
    public function getMembersWithOutstandingBalances()
    {
        $db = \Config\Database::connect();

        $query = $db->table('members m')
            ->join('collection_summary cs', 'm.member_id = cs.member_id')
            ->where('cs.balance > 0')
            ->where('m.status', 'active')
            ->get();

        return $query->getResultArray();
    }

    /**
     * Get paginated members with balance information
     *
     * @param int $limit
     * @param int $offset
     * @param string $search
     * @param string $orderBy
     * @param string $orderDir
     * @return array
     */
    public function getPaginatedMembersWithBalance($limit = 50, $offset = 0, $search = '', $orderBy = 'balance', $orderDir = 'DESC')
    {
        $db = \Config\Database::connect();

        $builder = $db->table('members m')
            ->select('m.*, COALESCE(cs.balance, 0) as balance, cs.last_payment_date')
            ->join('collection_summary cs', 'm.member_id = cs.member_id', 'left')
            ->where('m.status', 'active');

        if (!empty($search)) {
            $builder->groupStart()
                ->like('m.name', $search)
                ->orLike('m.phone', $search)
                ->orLike('m.whatsapp_number', $search)
                ->groupEnd();
        }

        // Validate order by column
        $allowedOrderBy = ['name', 'balance', 'join_date', 'last_payment_date'];
        if (!in_array($orderBy, $allowedOrderBy)) {
            $orderBy = 'balance';
        }

        $orderDir = strtoupper($orderDir) === 'ASC' ? 'ASC' : 'DESC';

        if ($orderBy === 'balance') {
            $builder->orderBy('COALESCE(cs.balance, 0)', $orderDir);
        } else {
            $builder->orderBy($orderBy, $orderDir);
        }

        $builder->limit($limit, $offset);

        return $builder->get()->getResultArray();
    }

    /**
     * Get total count of members for pagination
     *
     * @param string $search
     * @return int
     */
    public function getTotalMembersCount($search = '')
    {
        $builder = $this->where('status', 'active');

        if (!empty($search)) {
            $builder->groupStart()
                ->like('name', $search)
                ->orLike('phone', $search)
                ->orLike('whatsapp_number', $search)
                ->groupEnd();
        }

        return $builder->countAllResults();
    }
}
