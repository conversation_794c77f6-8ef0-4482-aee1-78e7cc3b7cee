<?= $this->extend('layout/main') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Payment Details</h1>
    <div>
        <a href="<?= site_url('payments/generate-receipt/' . $payment['payment_id']) ?>" class="btn btn-success">
            <i class="fas fa-file-invoice"></i> Generate Receipt
        </a>
        <a href="<?= site_url('payments/edit/' . $payment['payment_id']) ?>" class="btn btn-warning">
            <i class="fas fa-edit"></i> Edit Payment
        </a>
        <a href="<?= site_url('members/show/' . $payment['member_id']) ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Member
        </a>
    </div>
</div>

<div class="row">
    <!-- Payment Information -->
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">Payment Information</h5>
            </div>
            <div class="card-body">
                <table class="table table-striped">
                    <tr>
                        <th width="30%">ID</th>
                        <td><?= $payment['payment_id'] ?></td>
                    </tr>
                    <tr>
                        <th>Receipt Book #</th>
                        <td><?= $payment['receipt_book_number'] ?? '-' ?></td>
                    </tr>
                    <tr>
                        <th>Receipt Number</th>
                        <td><?= $payment['receipt_number'] ?></td>
                    </tr>
                    <tr>
                        <th>Member</th>
                        <td>
                            <a href="<?= site_url('members/show/' . $member['member_id']) ?>">
                                <?= $member['name'] ?>
                            </a>
                        </td>
                    </tr>
                    <tr>
                        <th>Amount</th>
                        <td><?= format_currency_with_decimals($payment['amount']) ?></td>
                    </tr>
                    <tr>
                        <th>Payment Date</th>
                        <td><?= date('d M Y', strtotime($payment['payment_date'])) ?></td>
                    </tr>
                    <tr>
                        <th>Payment Method</th>
                        <td><?= ucfirst(str_replace('_', ' ', $payment['payment_method'])) ?></td>
                    </tr>
                    <?php if (!empty($payment['payment_periods'])): ?>
                    <tr>
                        <th>Payment Periods</th>
                        <td>
                            <?php
                            $periods = json_decode($payment['payment_periods'], true);
                            if (is_array($periods) && !empty($periods)):
                                echo '<span class="badge bg-success">' . implode('</span> <span class="badge bg-success">', $periods) . '</span>';
                            else:
                                echo '-';
                            endif;
                            ?>
                        </td>
                    </tr>
                    <?php endif; ?>
                    <tr>
                        <th>Notes</th>
                        <td><?= $payment['notes'] ?? '-' ?></td>
                    </tr>
                    <tr>
                        <th>Created At</th>
                        <td><?= date('d M Y H:i', strtotime($payment['created_at'])) ?></td>
                    </tr>
                    <tr>
                        <th>Updated At</th>
                        <td><?= date('d M Y H:i', strtotime($payment['updated_at'])) ?></td>
                    </tr>
                </table>
            </div>
        </div>
    </div>

    <!-- Related Commitment -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">Related Commitment</h5>
            </div>
            <div class="card-body">
                <?php if ($commitment): ?>
                    <table class="table table-striped">
                        <tr>
                            <th width="30%">ID</th>
                            <td><?= $commitment['commitment_id'] ?></td>
                        </tr>
                        <tr>
                            <th>Amount</th>
                            <td><?= format_currency_with_decimals($commitment['amount']) ?></td>
                        </tr>
                        <tr>
                            <th>Frequency</th>
                            <td><?= ucfirst($commitment['frequency']) ?></td>
                        </tr>
                        <tr>
                            <th>Start Date</th>
                            <td><?= date('d M Y', strtotime($commitment['start_date'])) ?></td>
                        </tr>
                        <tr>
                            <th>End Date</th>
                            <td>
                                <?= $commitment['end_date'] ? date('d M Y', strtotime($commitment['end_date'])) : 'Ongoing' ?>
                            </td>
                        </tr>
                        <tr>
                            <th>Notes</th>
                            <td><?= $commitment['notes'] ?? '-' ?></td>
                        </tr>
                    </table>

                    <div class="mt-3">
                        <a href="<?= site_url('commitments/show/' . $commitment['commitment_id']) ?>" class="btn btn-sm btn-success">
                            <i class="fas fa-eye"></i> View Commitment Details
                        </a>
                    </div>
                <?php else: ?>
                    <p class="text-muted">This payment is not linked to any specific commitment.</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>
