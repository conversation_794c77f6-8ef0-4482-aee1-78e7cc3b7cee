<?= $this->extend('layout/main') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Payments</h1>
    <a href="<?= site_url('payments/new') ?>" class="btn btn-primary">
        <i class="fas fa-plus"></i> Add New Payment
    </a>
</div>

<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover datatable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Receipt Book #</th>
                        <th>Receipt #</th>
                        <th>Member</th>
                        <th>Commitment</th>
                        <th>Amount</th>
                        <th>Date</th>
                        <th>Method</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($payments as $payment): ?>
                        <tr>
                            <td><?= $payment['payment_id'] ?></td>
                            <td><?= $payment['receipt_book_number'] ?? '-' ?></td>
                            <td><?= $payment['receipt_number'] ?></td>
                            <td>
                                <a href="<?= site_url('members/show/' . $payment['member_id']) ?>">
                                    <?= $payment['member_name'] ?>
                                </a>
                            </td>
                            <td>
                                <?php if (!empty($payment['commitment_id'])): ?>
                                    <a href="<?= site_url('commitments/show/' . $payment['commitment_id']) ?>">
                                        <?php if (!empty($payment['payment_periods'])): ?>
                                            <?php
                                            $periods = json_decode($payment['payment_periods'], true);
                                            if (is_array($periods) && !empty($periods)):
                                                echo '<span class="badge bg-success">' . implode('</span> <span class="badge bg-success">', $periods) . '</span>';
                                            else:
                                                echo 'View';
                                            endif;
                                            ?>
                                        <?php else: ?>
                                            View
                                        <?php endif; ?>
                                    </a>
                                <?php else: ?>
                                    -
                                <?php endif; ?>
                            </td>
                            <td><?= format_currency_with_decimals($payment['amount']) ?></td>
                            <td><?= date('d M Y', strtotime($payment['payment_date'])) ?></td>
                            <td><?= ucfirst(str_replace('_', ' ', $payment['payment_method'])) ?></td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="<?= site_url('payments/show/' . $payment['payment_id']) ?>" class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="View">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="<?= site_url('payments/generate-receipt/' . $payment['payment_id']) ?>" class="btn btn-sm btn-success" data-bs-toggle="tooltip" title="Receipt">
                                        <i class="fas fa-file-invoice"></i>
                                    </a>
                                    <a href="<?= site_url('payments/edit/' . $payment['payment_id']) ?>" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal<?= $payment['payment_id'] ?>" title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>

                                <!-- Delete Modal -->
                                <div class="modal fade" id="deleteModal<?= $payment['payment_id'] ?>" tabindex="-1" aria-labelledby="deleteModalLabel<?= $payment['payment_id'] ?>" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="deleteModalLabel<?= $payment['payment_id'] ?>">Confirm Delete</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                Are you sure you want to delete this payment? This action cannot be undone.
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                <a href="<?= site_url('payments/delete/' . $payment['payment_id']) ?>" class="btn btn-danger">Delete</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?= $this->endSection() ?>
