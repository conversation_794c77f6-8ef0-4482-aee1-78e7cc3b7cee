<?= $this->extend('layout/main') ?>

<?= $this->section('content') ?>

<style>
    .member-active {
        color: #198754 !important;
        font-weight: bold;
    }

    .member-inactive {
        color: #dc3545 !important;
        font-style: italic;
    }

    .member-active:hover, .member-inactive:hover {
        text-decoration: underline;
    }
</style>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Members</h1>
    <a href="<?= site_url('members/new') ?>" class="btn btn-primary">
        <i class="fas fa-plus"></i> Add New Member
    </a>
</div>

<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover datatable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Name</th>
                        <th>Post Office</th>
                        <th>Phone</th>
                        <th>Join Date</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($members as $member): ?>
                        <tr>
                            <td><?= $member['member_id'] ?></td>
                            <td>
                                <a href="<?= site_url('members/show/' . $member['member_id']) ?>" class="<?= $member['status'] == 'active' ? 'member-active' : 'member-inactive' ?>">
                                    <?= $member['name'] ?>
                                </a>
                            </td>
                            <td><?= $member['post_office'] ?? '-' ?></td>
                            <td><?= $member['phone'] ?? '-' ?></td>
                            <td><?= date('d M Y', strtotime($member['join_date'])) ?></td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal<?= $member['member_id'] ?>" title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>

                                <!-- Delete Modal -->
                                <div class="modal fade" id="deleteModal<?= $member['member_id'] ?>" tabindex="-1" aria-labelledby="deleteModalLabel<?= $member['member_id'] ?>" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="deleteModalLabel<?= $member['member_id'] ?>">Confirm Delete</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                Are you sure you want to delete member <strong><?= $member['name'] ?></strong>? This action cannot be undone.
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                <a href="<?= site_url('members/delete/' . $member['member_id']) ?>" class="btn btn-danger">Delete</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?= $this->endSection() ?>
