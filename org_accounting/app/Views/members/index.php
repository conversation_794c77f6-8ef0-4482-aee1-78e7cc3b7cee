<?= $this->extend('layout/main') ?>

<?= $this->section('content') ?>

<style>
    .member-active {
        color: #198754 !important;
        font-weight: bold;
    }

    .member-inactive {
        color: #dc3545 !important;
        font-style: italic;
    }

    .member-active:hover, .member-inactive:hover {
        text-decoration: underline;
    }
</style>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Members <small class="text-muted">(<?= number_format($total_members) ?> total)</small></h1>
    <a href="<?= site_url('members/new') ?>" class="btn btn-success">
        <i class="fas fa-plus"></i> Add New Member
    </a>
</div>

<!-- Search and Filter Controls -->
<div class="card mb-3">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">Search Members</label>
                <input type="text" class="form-control" id="search" name="search" value="<?= esc($search) ?>" placeholder="Search by name, phone, or WhatsApp...">
            </div>
            <div class="col-md-3">
                <label for="order_by" class="form-label">Sort By</label>
                <select class="form-select" id="order_by" name="order_by">
                    <option value="balance" <?= $order_by === 'balance' ? 'selected' : '' ?>>Balance</option>
                    <option value="name" <?= $order_by === 'name' ? 'selected' : '' ?>>Name</option>
                    <option value="join_date" <?= $order_by === 'join_date' ? 'selected' : '' ?>>Join Date</option>
                    <option value="last_payment_date" <?= $order_by === 'last_payment_date' ? 'selected' : '' ?>>Last Payment</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="order_dir" class="form-label">Order</label>
                <select class="form-select" id="order_dir" name="order_dir">
                    <option value="DESC" <?= $order_dir === 'DESC' ? 'selected' : '' ?>>Descending</option>
                    <option value="ASC" <?= $order_dir === 'ASC' ? 'selected' : '' ?>>Ascending</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="per_page" class="form-label">Items per page</label>
                <select class="form-select" id="per_page" name="per_page">
                    <option value="25" <?= ($pagination['per_page'] ?? 50) == 25 ? 'selected' : '' ?>>25</option>
                    <option value="50" <?= ($pagination['per_page'] ?? 50) == 50 ? 'selected' : '' ?>>50</option>
                    <option value="100" <?= ($pagination['per_page'] ?? 50) == 100 ? 'selected' : '' ?>>100</option>
                    <option value="200" <?= ($pagination['per_page'] ?? 50) == 200 ? 'selected' : '' ?>>200</option>
                </select>
            </div>
            <div class="col-12">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i> Search
                </button>
                <a href="<?= site_url('members') ?>" class="btn btn-secondary">
                    <i class="fas fa-times"></i> Clear
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Results Info -->
<div class="d-flex justify-content-between align-items-center mb-3">
    <div>
        Showing <?= number_format($pagination['showing_from']) ?> to <?= number_format($pagination['showing_to']) ?> of <?= number_format($pagination['total_items']) ?> members
    </div>
</div>

<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Name</th>
                        <th>Post Office</th>
                        <th>Phone</th>
                        <th>Balance</th>
                        <th>Last Payment</th>
                        <th>Join Date</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($members)): ?>
                        <tr>
                            <td colspan="8" class="text-center py-4">
                                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                <p class="text-muted">No members found.</p>
                                <?php if (!empty($search)): ?>
                                    <a href="<?= site_url('members') ?>" class="btn btn-primary">View All Members</a>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($members as $member): ?>
                            <tr>
                                <td><?= $member['member_id'] ?></td>
                                <td>
                                    <a href="<?= site_url('members/show/' . $member['member_id']) ?>" class="<?= $member['status'] == 'active' ? 'member-active' : 'member-inactive' ?>">
                                        <?= esc($member['name']) ?>
                                    </a>
                                </td>
                                <td><?= esc($member['post_office'] ?? '-') ?></td>
                                <td>
                                    <?php if (!empty($member['phone'])): ?>
                                        <a href="tel:<?= $member['phone'] ?>" class="text-decoration-none">
                                            <i class="fas fa-phone text-primary"></i>
                                        </a>
                                        <?php if (!empty($member['whatsapp_number']) && $member['whatsapp_number'] !== $member['phone']): ?>
                                            <a href="https://wa.me/<?= preg_replace('/[^0-9]/', '', $member['whatsapp_number']) ?>" target="_blank" class="text-decoration-none ms-1">
                                                <i class="fab fa-whatsapp text-success"></i>
                                            </a>
                                        <?php elseif (!empty($member['whatsapp_number'])): ?>
                                            <a href="https://wa.me/<?= preg_replace('/[^0-9]/', '', $member['whatsapp_number']) ?>" target="_blank" class="text-decoration-none ms-1">
                                                <i class="fab fa-whatsapp text-success"></i>
                                            </a>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        -
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php
                                    $balance = $member['balance'] ?? 0;
                                    $balanceClass = $balance > 0 ? 'text-danger fw-bold' : 'text-muted';
                                    ?>
                                    <span class="<?= $balanceClass ?>">
                                        <?= $balance > 0 ? number_format($balance) : '0' ?>
                                    </span>
                                </td>
                                <td>
                                    <?php if (!empty($member['last_payment_date'])): ?>
                                        <?= date('d M Y', strtotime($member['last_payment_date'])) ?>
                                    <?php else: ?>
                                        <span class="text-muted">No payments</span>
                                    <?php endif; ?>
                                </td>
                                <td><?= date('d M Y', strtotime($member['join_date'])) ?></td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal<?= $member['member_id'] ?>" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>

                                    <!-- Delete Modal -->
                                    <div class="modal fade" id="deleteModal<?= $member['member_id'] ?>" tabindex="-1" aria-labelledby="deleteModalLabel<?= $member['member_id'] ?>" aria-hidden="true">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title" id="deleteModalLabel<?= $member['member_id'] ?>">Confirm Delete</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                </div>
                                                <div class="modal-body">
                                                    Are you sure you want to delete member <strong><?= esc($member['name']) ?></strong>? This action cannot be undone.
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                    <a href="<?= site_url('members/delete/' . $member['member_id']) ?>" class="btn btn-danger">Delete</a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Pagination -->
<?php if ($pagination['total_pages'] > 1): ?>
    <div class="d-flex justify-content-center mt-4">
        <?= \App\Libraries\PaginationHelper::generatePaginationLinks($pagination) ?>
    </div>
<?php endif; ?>

<?= $this->endSection() ?>
