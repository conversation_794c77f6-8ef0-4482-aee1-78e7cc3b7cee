<?= $this->extend('layout/main') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Collection Summary</h1>
    <div>
        <div class="btn-group me-2">
            <a href="<?= site_url('reports/collection-summary?exclude_zero=yes') ?>" class="btn btn-<?= $exclude_zero === 'yes' ? 'primary' : 'outline-primary' ?>">
                <i class="fas fa-filter"></i> Hide Zero Balances
            </a>
            <a href="<?= site_url('reports/collection-summary?exclude_zero=no') ?>" class="btn btn-<?= $exclude_zero === 'no' ? 'primary' : 'outline-primary' ?>">
                <i class="fas fa-list"></i> Show All
            </a>
        </div>
        <a href="<?= site_url('reports/recalculate-all-summaries') ?>" class="btn btn-warning" onclick="return confirm('Are you sure you want to recalculate all summaries? This may take a moment.');">
            <i class="fas fa-sync"></i> Recalculate All Summaries
        </a>
    </div>
</div>

<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover datatable">
                <thead>
                    <tr>
                        <th>Member</th>
                        <th class="text-center">Contact</th>
                        <th>Total Committed</th>
                        <th>Total Paid</th>
                        <th>Balance</th>
                        <th>Last Payment</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    $totalCommitted = 0;
                    $totalPaid = 0;
                    $totalBalance = 0;
                    ?>

                    <?php foreach ($summaries as $summary): ?>
                        <?php
                        $totalCommitted += $summary['total_committed'];
                        $totalPaid += $summary['total_paid'];
                        $totalBalance += $summary['balance'];
                        ?>
                        <tr>
                            <td>
                                <a href="<?= site_url('members/show/' . $summary['member_id']) ?>" class="<?= $summary['status'] == 'active' ? 'text-success fw-bold' : 'text-danger' ?>">
                                    <?= $summary['member_name'] ?>
                                </a>
                            </td>
                            <td class="text-center">
                                <div class="d-flex justify-content-center gap-3">
                                    <?php if (!empty($summary['whatsapp_number'])): ?>
                                        <a href="https://wa.me/<?= preg_replace('/[^0-9]/', '', $summary['whatsapp_number']) ?>?text=Hello%20<?= urlencode($summary['member_name']) ?>,%20this%20is%20a%20reminder%20about%20your%20outstanding%20balance%20of%20<?= urlencode(number_format($summary['balance'], 0)) ?>." target="_blank" class="text-decoration-none" data-bs-toggle="tooltip" title="WhatsApp">
                                            <i class="fab fa-whatsapp text-success fa-lg"></i>
                                        </a>
                                    <?php endif; ?>

                                    <?php if (!empty($summary['phone'])): ?>
                                        <a href="tel:<?= $summary['phone'] ?>" class="text-decoration-none" data-bs-toggle="tooltip" title="Call">
                                            <i class="fas fa-phone-alt text-success fa-lg"></i>
                                        </a>
                                    <?php endif; ?>

                                    <?php if (empty($summary['phone']) && empty($summary['whatsapp_number'])): ?>
                                        <span class="text-muted">—</span>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td><?= number_format($summary['total_committed'], 0) ?></td>
                            <td><?= number_format($summary['total_paid'], 0) ?></td>
                            <td class="<?= $summary['balance'] > 0 ? 'text-danger fw-bold' : 'text-success' ?>">
                                <?= number_format($summary['balance'], 0) ?>
                            </td>
                            <td>
                                <?php if ($summary['last_payment_date']): ?>
                                    <?= date('d M Y', strtotime($summary['last_payment_date'])) ?>
                                    <br>
                                    <small class="text-muted">
                                        Amount: <?= number_format($summary['last_payment_amount'], 0) ?>
                                    </small>
                                <?php else: ?>
                                    <span class="text-muted">No payments</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($summary['status'] == 'active'): ?>
                                    <span class="badge bg-success">Active</span>
                                <?php else: ?>
                                    <span class="badge bg-danger">Inactive</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
                <tfoot>
                    <tr class="table-dark">
                        <th>TOTAL</th>
                        <th></th>
                        <th><?= number_format($totalCommitted, 0) ?></th>
                        <th><?= number_format($totalPaid, 0) ?></th>
                        <th><?= number_format($totalBalance, 0) ?></th>
                        <th></th>
                        <th></th>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
</div>

<?= $this->endSection() ?>
