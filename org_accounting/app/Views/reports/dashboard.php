<?= $this->extend('layout/main') ?>

<?= $this->section('content') ?>

<h1 class="mb-4">Dashboard</h1>

<div class="row">
    <!-- Total Members Card -->
    <div class="col-md-3">
        <div class="card text-white bg-primary">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title">Total Members</h6>
                        <h2 class="card-text"><?= $totalMembers ?></h2>
                    </div>
                    <i class="fas fa-users fa-3x"></i>
                </div>
                <p class="card-text mt-2">Active: <?= $activeMembers ?></p>
            </div>
        </div>
    </div>

    <!-- Total Commitments Card -->
    <div class="col-md-3">
        <div class="card text-white bg-success">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title">Total Commitments</h6>
                        <h2 class="card-text"><?= number_format($totalCommitments, 2) ?></h2>
                    </div>
                    <i class="fas fa-handshake fa-3x"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Total Payments Card -->
    <div class="col-md-3">
        <div class="card text-white bg-info">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title">Total Payments</h6>
                        <h2 class="card-text"><?= number_format($totalPayments, 2) ?></h2>
                    </div>
                    <i class="fas fa-money-bill-wave fa-3x"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Outstanding Balance Card -->
    <div class="col-md-3">
        <div class="card text-white bg-danger">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title">Outstanding Balance</h6>
                        <h2 class="card-text"><?= number_format($totalOutstanding, 2) ?></h2>
                    </div>
                    <i class="fas fa-exclamation-circle fa-3x"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">


    <!-- Outstanding Balances -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-danger text-white">
                <h5 class="card-title mb-0">Outstanding Balances</h5>
            </div>
            <div class="card-body">
                <?php if (empty($membersWithOutstandingBalances)): ?>
                    <p class="text-muted">No outstanding balances found.</p>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Member</th>
                                    <th>Phone</th>
                                    <th>Balance</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($membersWithOutstandingBalances as $member): ?>
                                    <tr>
                                        <td>
                                            <a href="<?= site_url('members/show/' . $member['member_id']) ?>" data-bs-toggle="tooltip" title="View Member">
                                                <?= $member['member_name'] ?>
                                            </a>
                                        </td>
                                        <td>
                                            <?php if (!empty($member['phone'])): ?>
                                                <div class="d-flex align-items-center justify-content-center gap-3">
                                                    <a href="https://wa.me/<?= preg_replace('/[^0-9]/', '', $member['phone']) ?>?text=Hello%20<?= urlencode($member['member_name']) ?>,%20this%20is%20a%20reminder%20about%20your%20outstanding%20balance%20of%20<?= urlencode(number_format($member['balance'], 0)) ?>." target="_blank" class="text-decoration-none" data-bs-toggle="tooltip" title="WhatsApp">
                                                        <i class="fab fa-whatsapp text-success fa-lg"></i>
                                                    </a>
                                                    <a href="tel:<?= $member['phone'] ?>" class="text-decoration-none" data-bs-toggle="tooltip" title="Call">
                                                        <i class="fas fa-phone-alt text-success fa-lg"></i>
                                                    </a>
                                                </div>
                                            <?php else: ?>
                                                <span class="text-muted">Not available</span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="text-danger fw-bold"><?= number_format($member['balance'], 0) ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="text-end">
                        <a href="<?= site_url('reports/outstanding-balances') ?>" class="btn btn-sm btn-danger">View All Outstanding Balances</a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Members Without Active Commitments -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h5 class="card-title mb-0">Members Without Active Commitments</h5>
            </div>
            <div class="card-body">
                <?php if (empty($membersWithoutActiveCommitments)): ?>
                    <p class="text-muted">All active members have active commitments.</p>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Member</th>
                                    <th>Phone</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($membersWithoutActiveCommitments as $member): ?>
                                    <tr>
                                        <td>
                                            <a href="<?= site_url('members/show/' . $member['member_id']) ?>" data-bs-toggle="tooltip" title="View Member">
                                                <?= $member['name'] ?>
                                            </a>
                                        </td>
                                        <td>
                                            <?php if (!empty($member['phone'])): ?>
                                                <div class="d-flex align-items-center">
                                                    <a href="https://wa.me/<?= preg_replace('/[^0-9]/', '', $member['phone']) ?>?text=Hello%20<?= urlencode($member['name']) ?>,%20we%20noticed%20you%20don't%20have%20any%20active%20commitments." target="_blank" class="text-decoration-none me-2" data-bs-toggle="tooltip" title="WhatsApp">
                                                        <i class="fab fa-whatsapp text-success fa-lg"></i>
                                                    </a>
                                                    <span class="me-2"><?= $member['phone'] ?></span>
                                                    <a href="tel:<?= $member['phone'] ?>" class="text-decoration-none" data-bs-toggle="tooltip" title="Call">
                                                        <i class="fas fa-phone-alt text-success fa-lg"></i>
                                                    </a>
                                                </div>
                                            <?php else: ?>
                                                <span class="text-muted">Not available</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <a href="<?= site_url('commitments/new?member_id=' . $member['member_id']) ?>" class="btn btn-sm btn-success" data-bs-toggle="tooltip" title="Add Commitment">
                                                <i class="fas fa-plus"></i> Add
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="text-end">
                        <a href="<?= site_url('reports/members-without-commitments') ?>" class="btn btn-sm btn-warning">View All Inactive Commitments</a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Collection Efficiency Dashboard -->
<div class="row mt-4">
    <div class="col-12">
        <h4 class="mb-3">Collection Efficiency Dashboard</h4>
    </div>

    <!-- Current Month Collection Rate -->
    <div class="col-md-3">
        <div class="card border-primary">
            <div class="card-body text-center">
                <h5 class="card-title text-primary">Current Month Collection</h5>
                <div class="display-4 fw-bold"><?= $collectionEfficiencyData['currentMonthRate'] ?>%</div>
                <p class="card-text text-muted">Payments / Commitments</p>
            </div>
        </div>
    </div>

    <!-- Year-to-date Collection Rate -->
    <div class="col-md-3">
        <div class="card border-success">
            <div class="card-body text-center">
                <h5 class="card-title text-success">Year-to-date Collection</h5>
                <div class="display-4 fw-bold"><?= $collectionEfficiencyData['ytdRate'] ?>%</div>
                <p class="card-text text-muted">YTD Payments / Commitments</p>
            </div>
        </div>
    </div>

    <!-- Average Months to Payment -->
    <div class="col-md-3">
        <div class="card border-info">
            <div class="card-body text-center">
                <h5 class="card-title text-info">Avg. Months to Payment</h5>
                <div class="display-4 fw-bold"><?= $collectionEfficiencyData['avgMonths'] ?></div>
                <p class="card-text text-muted">From commitment to payment</p>
            </div>
        </div>
    </div>

    <!-- Percentage with Outstanding Balance -->
    <div class="col-md-3">
        <div class="card border-danger">
            <div class="card-body text-center">
                <h5 class="card-title text-danger">Members with Balance</h5>
                <div class="display-4 fw-bold"><?= $collectionEfficiencyData['percentWithBalance'] ?>%</div>
                <p class="card-text text-muted">Of active members with outstanding balances</p>
            </div>
        </div>
    </div>
</div>

<!-- Collection Summary Section -->
<div class="row mt-4">
    <div class="col-md-12">
        <h4 class="mb-3">Collection Summary</h4>

        <!-- Date Filter Form -->
        <div class="card mb-3">
            <div class="card-body">
                <form id="dateRangeForm" class="row g-3">
                    <div class="col-md-4">
                        <label for="startDate" class="form-label">Start Date</label>
                        <input type="date" class="form-control" id="startDate" name="start_date" value="<?= $startDate ?>">
                    </div>
                    <div class="col-md-4">
                        <label for="endDate" class="form-label">End Date</label>
                        <input type="date" class="form-control" id="endDate" name="end_date" value="<?= $endDate ?>">
                    </div>
                    <div class="col-md-4 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">Apply Filter</button>
                        <button type="button" id="resetDates" class="btn btn-secondary">Reset to Last 12 Months</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Collection Summary Chart -->
        <div class="card">
            <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Monthly Collection Summary</h5>
                <div>
                    <button onclick="window.print()" class="btn btn-sm btn-light me-2">
                        <i class="fas fa-print"></i> Print Chart
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="chart-container" style="position: relative; height:50vh; width:100%; margin-top: 40px;">
                    <canvas id="statisticsChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Additional Charts Row -->
<div class="row mt-4">
    <!-- Member Payment Compliance Chart -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">Member Payment Compliance</h5>
            </div>
            <div class="card-body">
                <div class="chart-container" style="position: relative; height:40vh; width:100%">
                    <canvas id="memberComplianceChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Method Distribution -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="card-title mb-0">Payment Method Distribution</h5>
            </div>
            <div class="card-body">
                <div class="chart-container" style="position: relative; height:40vh; width:100%">
                    <canvas id="paymentMethodChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Payment Trend Analysis -->
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h5 class="card-title mb-0">Payment Trend Analysis</h5>
            </div>
            <div class="card-body">
                <div class="chart-container" style="position: relative; height:40vh; width:100%">
                    <canvas id="paymentTrendChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<style type="text/css" media="print">
    @media print {
        .sidebar, .navbar, .card-header button, .card-header a, .btn, form {
            display: none !important;
        }
        .content {
            margin-left: 0 !important;
            padding: 0 !important;
        }
        .card {
            border: none !important;
        }
        .card-header {
            background-color: #f8f9fa !important;
            color: #000 !important;
        }
        .chart-container {
            height: 80vh !important;
        }
    }
</style>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<!-- Chart.js and plugins -->
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2.0.0/dist/chartjs-plugin-datalabels.min.js"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Register the plugin to all charts
        Chart.register(ChartDataLabels);

        // Get data from PHP
        const months = <?= $months ?>;
        const committedAmounts = <?= $committedAmounts ?>;
        const paidAmounts = <?= $paidAmounts ?>;
        const datePaidAmounts = <?= $datePaidAmounts ?>;
        const fulfillmentRates = <?= $fulfillmentRates ?>;
        const movingAverages = <?= $movingAverages ?>;
        const memberComplianceData = <?= $memberComplianceData ?>;
        const paymentMethodData = <?= $paymentMethodData ?>;

        // Calculate balance amounts for collection summary
        const balanceAmounts = committedAmounts.map((committed, index) => committed - paidAmounts[index]);

        // Create chart
        const ctx = document.getElementById('statisticsChart').getContext('2d');
        const statisticsChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: months,
                datasets: [
                    {
                        label: 'Total Committed',
                        data: committedAmounts,
                        backgroundColor: 'rgba(54, 162, 235, 0.7)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 1,
                        order: 3
                    },
                    {
                        label: 'Total Paid',
                        data: paidAmounts,
                        backgroundColor: 'rgba(75, 192, 192, 0.7)',
                        borderColor: 'rgba(75, 192, 192, 1)',
                        borderWidth: 1,
                        order: 2
                    },
                    {
                        label: 'Outstanding Balance',
                        data: balanceAmounts,
                        backgroundColor: 'rgba(255, 99, 132, 0.7)',
                        borderColor: 'rgba(255, 99, 132, 1)',
                        borderWidth: 1,
                        order: 1
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                layout: {
                    padding: {
                        top: 50,
                        bottom: 20
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Amount'
                        },
                        ticks: {
                            callback: function(value) {
                                return value.toLocaleString();
                            }
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Month'
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                label += context.parsed.y.toLocaleString();
                                return label;
                            }
                        }
                    },
                    legend: {
                        position: 'top',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    },
                    title: {
                        display: true,
                        text: 'Monthly Collection Summary'
                    },
                    datalabels: {
                        anchor: 'end',
                        align: 'top',
                        formatter: function(value) {
                            return value.toLocaleString();
                        },
                        font: {
                            weight: 'bold'
                        }
                    }
                }
            }
        });

        // Handle date range form submission
        document.getElementById('dateRangeForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;

            // Redirect to the same page with date parameters
            window.location.href = '<?= site_url('reports') ?>?start_date=' + startDate + '&end_date=' + endDate;
        });

        // Handle reset button
        document.getElementById('resetDates').addEventListener('click', function() {
            // Calculate date 12 months ago
            const today = new Date();
            const twelveMonthsAgo = new Date();
            twelveMonthsAgo.setMonth(today.getMonth() - 12);

            // Format dates as YYYY-MM-DD
            const formatDate = (date) => {
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                return `${year}-${month}-${day}`;
            };

            // Set the start and end dates
            document.getElementById('startDate').value = formatDate(twelveMonthsAgo);
            document.getElementById('endDate').value = formatDate(today);

            // Submit the form
            document.getElementById('dateRangeForm').dispatchEvent(new Event('submit'));
        });

        // Create Member Payment Compliance Chart (Pie Chart)
        const memberComplianceCtx = document.getElementById('memberComplianceChart').getContext('2d');
        const memberComplianceChart = new Chart(memberComplianceCtx, {
            type: 'pie',
            data: {
                labels: memberComplianceData.labels,
                datasets: [{
                    data: memberComplianceData.data,
                    backgroundColor: memberComplianceData.backgroundColor,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = Math.round((value / total) * 100);
                                return `${label}: ${value} members (${percentage}%)`;
                            }
                        }
                    },
                    datalabels: {
                        formatter: function(value, context) {
                            return value > 0 ? value : '';
                        },
                        color: '#fff',
                        font: {
                            weight: 'bold',
                            size: 12
                        }
                    }
                }
            }
        });

        // Create Payment Method Distribution Chart (Donut Chart)
        const paymentMethodCtx = document.getElementById('paymentMethodChart').getContext('2d');
        const paymentMethodChart = new Chart(paymentMethodCtx, {
            type: 'doughnut',
            data: {
                labels: paymentMethodData.labels,
                datasets: [{
                    data: paymentMethodData.data,
                    backgroundColor: paymentMethodData.backgroundColor,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutout: '50%',
                plugins: {
                    legend: {
                        position: 'right',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = Math.round((value / total) * 100);
                                return `${label}: ${value} payments (${percentage}%)`;
                            }
                        }
                    },
                    datalabels: {
                        formatter: function(value, context) {
                            return value > 0 ? value : '';
                        },
                        color: '#fff',
                        font: {
                            weight: 'bold',
                            size: 12
                        }
                    }
                }
            }
        });

        // Create Payment Trend Analysis Chart (Line Chart)
        const paymentTrendCtx = document.getElementById('paymentTrendChart').getContext('2d');
        const paymentTrendChart = new Chart(paymentTrendCtx, {
            type: 'line',
            data: {
                labels: months,
                datasets: [
                    {
                        label: 'Monthly Payments (by Date)',
                        data: datePaidAmounts,
                        borderColor: 'rgba(75, 192, 192, 1)',
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.1
                    },
                    {
                        label: '3-Month Moving Average',
                        data: movingAverages,
                        borderColor: 'rgba(255, 159, 64, 1)',
                        borderWidth: 2,
                        borderDash: [5, 5],
                        fill: false,
                        pointRadius: 0
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Amount'
                        },
                        ticks: {
                            callback: function(value) {
                                return value.toLocaleString();
                            }
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Month'
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                label += context.parsed.y.toLocaleString();
                                return label;
                            }
                        }
                    },
                    legend: {
                        position: 'top',
                    },
                    datalabels: {
                        display: false
                    }
                }
            }
        });
    });
</script>
<?= $this->endSection() ?>
