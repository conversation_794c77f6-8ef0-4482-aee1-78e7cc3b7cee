<?php

namespace App\Libraries;

use CodeIgniter\Session\Handlers\FileHandler;
use CodeIgniter\Session\Exceptions\SessionException;
use Config\Session as SessionConfig;

/**
 * Custom File Handler that fixes the savePath override issue
 */
class CustomFileHandler extends FileHandler
{
    /**
     * Store the original configured save path
     */
    private string $configuredSavePath;

    public function __construct(SessionConfig $config, string $ipAddress)
    {
        parent::__construct($config, $ipAddress);

        // Store the configured save path before it gets overridden
        $this->configuredSavePath = $this->savePath;
    }

    /**
     * Re-initialize existing session, or creates a new one.
     *
     * This method fixes the issue where P<PERSON>'s session handler
     * overrides our configured savePath with its own (often empty) path.
     *
     * @param string $path The path where to store/retrieve the session (from PHP)
     * @param string $name The session name
     *
     * @throws SessionException
     */
    public function open($path, $name): bool
    {
        // CRITICAL FIX: Use our configured path instead of <PERSON><PERSON>'s path
        // <PERSON><PERSON> passes an empty/null path which causes the "null" directory issue
        $actualPath = $this->configuredSavePath;

        // Ensure the directory exists
        if (! is_dir($actualPath)) {
            if (! mkdir($actualPath, 0700, true)) {
                throw SessionException::forInvalidSavePath($actualPath);
            }
        }

        // Ensure the directory is writable
        if (! is_writable($actualPath)) {
            throw SessionException::forWriteProtectedSavePath($actualPath);
        }

        // Set the savePath to our configured path, not PHP's path
        $this->savePath = $actualPath;

        // Build the file path using our configured path
        $this->filePath = $this->savePath . '/' . $name . ($this->matchIP ? md5($this->ipAddress) : '');

        return true;
    }
}
