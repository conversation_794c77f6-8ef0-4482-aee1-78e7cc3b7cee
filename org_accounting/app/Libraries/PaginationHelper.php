<?php

namespace App\Libraries;

use CodeIgniter\Pager\Pager;

/**
 * Pagination Helper for Performance Optimization
 */
class PaginationHelper
{
    /**
     * Default items per page
     */
    const DEFAULT_PER_PAGE = 50;
    
    /**
     * Maximum items per page (to prevent memory issues)
     */
    const MAX_PER_PAGE = 200;
    
    /**
     * Get pagination parameters from request
     *
     * @param \CodeIgniter\HTTP\RequestInterface $request
     * @return array
     */
    public static function getPaginationParams($request)
    {
        $page = max(1, (int) $request->getGet('page') ?: 1);
        $perPage = min(
            self::MAX_PER_PAGE, 
            max(10, (int) $request->getGet('per_page') ?: self::DEFAULT_PER_PAGE)
        );
        
        return [
            'page' => $page,
            'per_page' => $perPage,
            'offset' => ($page - 1) * $perPage
        ];
    }
    
    /**
     * Create pagination data for views
     *
     * @param int $totalItems
     * @param int $currentPage
     * @param int $perPage
     * @param string $baseUrl
     * @return array
     */
    public static function createPaginationData($totalItems, $currentPage, $perPage, $baseUrl)
    {
        $totalPages = ceil($totalItems / $perPage);
        
        return [
            'current_page' => $currentPage,
            'per_page' => $perPage,
            'total_items' => $totalItems,
            'total_pages' => $totalPages,
            'has_previous' => $currentPage > 1,
            'has_next' => $currentPage < $totalPages,
            'previous_page' => max(1, $currentPage - 1),
            'next_page' => min($totalPages, $currentPage + 1),
            'base_url' => $baseUrl,
            'showing_from' => (($currentPage - 1) * $perPage) + 1,
            'showing_to' => min($totalItems, $currentPage * $perPage)
        ];
    }
    
    /**
     * Generate pagination links HTML
     *
     * @param array $paginationData
     * @return string
     */
    public static function generatePaginationLinks($paginationData)
    {
        if ($paginationData['total_pages'] <= 1) {
            return '';
        }
        
        $html = '<nav aria-label="Page navigation">';
        $html .= '<ul class="pagination justify-content-center">';
        
        // Previous button
        if ($paginationData['has_previous']) {
            $html .= '<li class="page-item">';
            $html .= '<a class="page-link" href="' . $paginationData['base_url'] . '?page=' . $paginationData['previous_page'] . '&per_page=' . $paginationData['per_page'] . '">Previous</a>';
            $html .= '</li>';
        } else {
            $html .= '<li class="page-item disabled"><span class="page-link">Previous</span></li>';
        }
        
        // Page numbers (show max 5 pages around current)
        $start = max(1, $paginationData['current_page'] - 2);
        $end = min($paginationData['total_pages'], $paginationData['current_page'] + 2);
        
        if ($start > 1) {
            $html .= '<li class="page-item"><a class="page-link" href="' . $paginationData['base_url'] . '?page=1&per_page=' . $paginationData['per_page'] . '">1</a></li>';
            if ($start > 2) {
                $html .= '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
        }
        
        for ($i = $start; $i <= $end; $i++) {
            if ($i == $paginationData['current_page']) {
                $html .= '<li class="page-item active"><span class="page-link">' . $i . '</span></li>';
            } else {
                $html .= '<li class="page-item"><a class="page-link" href="' . $paginationData['base_url'] . '?page=' . $i . '&per_page=' . $paginationData['per_page'] . '">' . $i . '</a></li>';
            }
        }
        
        if ($end < $paginationData['total_pages']) {
            if ($end < $paginationData['total_pages'] - 1) {
                $html .= '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
            $html .= '<li class="page-item"><a class="page-link" href="' . $paginationData['base_url'] . '?page=' . $paginationData['total_pages'] . '&per_page=' . $paginationData['per_page'] . '">' . $paginationData['total_pages'] . '</a></li>';
        }
        
        // Next button
        if ($paginationData['has_next']) {
            $html .= '<li class="page-item">';
            $html .= '<a class="page-link" href="' . $paginationData['base_url'] . '?page=' . $paginationData['next_page'] . '&per_page=' . $paginationData['per_page'] . '">Next</a>';
            $html .= '</li>';
        } else {
            $html .= '<li class="page-item disabled"><span class="page-link">Next</span></li>';
        }
        
        $html .= '</ul>';
        $html .= '</nav>';
        
        return $html;
    }
}
