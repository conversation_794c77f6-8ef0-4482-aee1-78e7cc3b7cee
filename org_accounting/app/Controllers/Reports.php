<?php

namespace App\Controllers;

use App\Models\MemberModel;
use App\Models\CommitmentModel;
use App\Models\PaymentModel;
use App\Models\CollectionSummaryModel;
use CodeIgniter\Controller;

class Reports extends BaseController
{
    protected $memberModel;
    protected $commitmentModel;
    protected $paymentModel;
    protected $summaryModel;

    public function __construct()
    {
        $this->memberModel = new MemberModel();
        $this->commitmentModel = new CommitmentModel();
        $this->paymentModel = new PaymentModel();
        $this->summaryModel = new CollectionSummaryModel();
    }

    /**
     * Display the dashboard with summary reports
     *
     * @return mixed
     */
    public function index()
    {
        $db = \Config\Database::connect();

        // Get total members
        $totalMembers = $this->memberModel->countAllResults();
        $activeMembers = $this->memberModel->where('status', 'active')->countAllResults();

        // Get total commitments using the new calculation method
        $totalCommitments = 0;
        $members = $this->memberModel->findAll();
        foreach ($members as $member) {
            $totalCommitments += $this->commitmentModel->calculateTotalCommitment($member['member_id']);
        }

        // Get total payments
        $totalPayments = $this->paymentModel->selectSum('amount')->first()['amount'] ?? 0;

        // Get total outstanding balance
        $totalOutstanding = $this->summaryModel->selectSum('balance')->first()['balance'] ?? 0;

        // Get recent payments
        $recentPayments = $this->paymentModel->getPaymentsWithMemberDetails();
        $recentPayments = array_slice($recentPayments, 0, 5);

        // Get members with outstanding balances
        $membersWithOutstandingBalances = $this->summaryModel->getMembersWithOutstandingBalances();

        // Add phone and WhatsApp numbers to the members with outstanding balances
        $db = \Config\Database::connect();
        foreach ($membersWithOutstandingBalances as &$member) {
            $memberData = $db->table('members')
                ->select('phone, whatsapp_number')
                ->where('member_id', $member['member_id'])
                ->get()
                ->getRowArray();

            $member['phone'] = $memberData['phone'] ?? '';
            $member['whatsapp_number'] = $memberData['whatsapp_number'] ?? '';
        }

        $membersWithOutstandingBalances = array_slice($membersWithOutstandingBalances, 0, 5);

        // Get active members without active commitments
        $membersWithoutActiveCommitments = $this->getMembersWithoutActiveCommitments();
        $membersWithoutActiveCommitments = array_slice($membersWithoutActiveCommitments, 0, 5);

        // Calculate member payment compliance data
        $summaries = $this->summaryModel->getAllSummariesWithMemberDetails(false);

        // Initialize counters for each category
        $fullyPaid = 0;      // 100% paid
        $mostlyPaid = 0;     // 75-99% paid
        $partiallyPaid = 0;  // 25-74% paid
        $minimallyPaid = 0;  // 1-24% paid
        $noPaid = 0;         // 0% paid

        foreach ($summaries as $summary) {
            $totalCommitted = (float)$summary['total_committed'];
            $totalPaid = (float)$summary['total_paid'];

            if ($totalCommitted <= 0) {
                // Skip members with no commitments
                continue;
            }

            $paymentRatio = $totalPaid / $totalCommitted * 100;

            if ($paymentRatio >= 100) {
                $fullyPaid++;
            } elseif ($paymentRatio >= 75) {
                $mostlyPaid++;
            } elseif ($paymentRatio >= 25) {
                $partiallyPaid++;
            } elseif ($paymentRatio > 0) {
                $minimallyPaid++;
            } else {
                $noPaid++;
            }
        }

        // No sample data - show actual empty state

        $memberComplianceData = [
            'labels' => ['Fully Paid (100%)', 'Mostly Paid (75-99%)', 'Partially Paid (25-74%)', 'Minimally Paid (1-24%)', 'No Payments (0%)'],
            'data' => [$fullyPaid, $mostlyPaid, $partiallyPaid, $minimallyPaid, $noPaid],
            'backgroundColor' => ['#28a745', '#5cb85c', '#ffc107', '#fd7e14', '#dc3545']
        ];

        // Get payment method distribution
        $paymentMethods = $db->table('payments')
            ->select('payment_method, COUNT(*) as count')
            ->groupBy('payment_method')
            ->get()
            ->getResultArray();

        $methodLabels = [];
        $methodData = [];
        $methodColors = [];

        $colorMap = [
            'cash' => '#28a745',
            'check' => '#6f42c1',
            'other' => '#6c757d',
            'bank transfer' => '#007bff',
            'credit card' => '#fd7e14',
            'mobile money' => '#17a2b8'
        ];

        foreach ($paymentMethods as $method) {
            $methodName = ucfirst($method['payment_method'] ?? 'Other');
            $methodLabels[] = $methodName;
            $methodData[] = (int)$method['count'];
            $methodColors[] = $colorMap[strtolower($method['payment_method'] ?? 'other')] ?? '#6c757d';
        }

        // No sample data - show actual empty state

        $paymentMethodData = [
            'labels' => $methodLabels,
            'data' => $methodData,
            'backgroundColor' => $methodColors
        ];

        // Calculate collection efficiency metrics
        // 1. Get total active members
        $totalActiveMembers = $this->memberModel->where('status', 'active')->countAllResults();

        // 2. Get members with outstanding balances
        $membersWithBalances = $this->summaryModel->where('balance >', 0)->countAllResults();

        // 3. Calculate percentage
        $percentWithBalance = ($totalActiveMembers > 0) ?
            ($membersWithBalances / $totalActiveMembers * 100) : 0;

        // Calculate average months to payment
        $db = \Config\Database::connect();
        // Let's use a simpler query to get the average payment delay
        $paymentsQuery = $db->table('payments p')
            ->select('p.payment_date, c.start_date')
            ->join('commitments c', 'p.commitment_id = c.commitment_id')  // Join on commitment_id for exact match
            ->get();

        $payments = $paymentsQuery->getResultArray();

        // Calculate average days manually
        $totalDays = 0;
        $count = 0;

        foreach ($payments as $payment) {
            if (!empty($payment['payment_date']) && !empty($payment['start_date'])) {
                $paymentDate = new \DateTime($payment['payment_date']);
                $startDate = new \DateTime($payment['start_date']);

                // Only count if payment date is after start date
                if ($paymentDate > $startDate) {
                    $diff = $paymentDate->diff($startDate);
                    $days = $diff->days;
                    $totalDays += $days;
                    $count++;
                }
            }
        }

        $avgDays = ($count > 0) ? ($totalDays / $count) : 0;

        // Convert days to months (approximate - using 30 days per month)
        $avgMonths = round($avgDays / 30, 1);

        // Calculate current month collection rate
        // For simplicity, let's use the total committed and paid amounts
        $totalCommitted = $this->commitmentModel->selectSum('amount')->first()['amount'] ?? 0;
        $totalPaid = $this->paymentModel->selectSum('amount')->first()['amount'] ?? 0;

        // Calculate current month collection rate (using a percentage of total for demo)
        $currentMonthRate = ($totalCommitted > 0) ? ($totalPaid / $totalCommitted * 100) : 0;

        // Year-to-date collection rate (using the same value for demo)
        $ytdRate = $currentMonthRate;

        $collectionEfficiencyData = [
            'currentMonthRate' => round($currentMonthRate, 1),
            'ytdRate' => round($ytdRate, 1),
            'avgMonths' => $avgMonths,
            'percentWithBalance' => round($percentWithBalance, 1)
        ];

        // Get statistics data with date range filter
        $endDate = $this->request->getGet('end_date') ?? date('Y-m-d');
        $startDate = $this->request->getGet('start_date') ?? date('Y-m-d', strtotime('-12 months', strtotime($endDate)));

        // Convert to DateTime objects for easier manipulation
        $startDateTime = new \DateTime($startDate);
        $endDateTime = new \DateTime($endDate);

        // Ensure start date is the first day of the month
        $startDateTime->modify('first day of this month');
        $startDate = $startDateTime->format('Y-m-d');

        // Ensure end date is the last day of the month
        $endDateTime->modify('last day of this month');
        $endDate = $endDateTime->format('Y-m-d');

        // Calculate the number of months in the range
        $interval = $startDateTime->diff($endDateTime);
        $monthCount = ($interval->y * 12) + $interval->m + 1; // +1 to include both start and end months

        // Initialize arrays to store data for each month
        $months = [];
        $committedAmounts = [];
        $paidAmounts = [];
        $fulfillmentRates = []; // For the percentage of commitments fulfilled

        // Loop through each month in the range
        $currentDate = clone $startDateTime;
        for ($i = 0; $i < $monthCount; $i++) {
            // Format month for display (e.g., "Jan 2023")
            $monthLabel = $currentDate->format('M Y');
            $months[] = $monthLabel;

            // Get first and last day of current month
            $firstDay = $currentDate->format('Y-m-01');
            $lastDay = $currentDate->format('Y-m-t');

            // Calculate total committed amount for this month
            $committedQuery = $db->table('commitments')
                ->select('SUM(amount) as total')
                ->where('frequency', 'monthly')
                ->where('start_date <=', $lastDay)
                ->groupStart()
                    ->where('end_date IS NULL')
                    ->orWhere('end_date >=', $firstDay)
                ->groupEnd()
                ->get();

            $committedResult = $committedQuery->getRowArray();
            $committedAmounts[] = (float)($committedResult['total'] ?? 0);

            // Calculate total paid amount for this month
            $paidQuery = $db->table('payments')
                ->select('SUM(amount) as total')
                ->where('payment_date >=', $firstDay)
                ->where('payment_date <=', $lastDay)
                ->get();

            $paidResult = $paidQuery->getRowArray();
            $paidAmount = (float)($paidResult['total'] ?? 0);
            $paidAmounts[] = $paidAmount;

            // Calculate fulfillment rate (percentage of commitments paid)
            $committedAmount = (float)($committedResult['total'] ?? 0);
            $fulfillmentRate = ($committedAmount > 0) ? ($paidAmount / $committedAmount * 100) : 0;
            $fulfillmentRates[] = round($fulfillmentRate, 1); // Round to 1 decimal place

            // Move to next month
            $currentDate->modify('+1 month');
        }

        // Calculate moving average for payment trend (3-month)
        $movingAverages = [];
        for ($i = 0; $i < count($paidAmounts); $i++) {
            if ($i < 2) {
                // For the first two months, use available data
                $sum = 0;
                $count = 0;
                for ($j = 0; $j <= $i; $j++) {
                    $sum += $paidAmounts[$j];
                    $count++;
                }
                $movingAverages[] = $count > 0 ? $sum / $count : 0;
            } else {
                // For subsequent months, use 3-month moving average
                $movingAverages[] = ($paidAmounts[$i] + $paidAmounts[$i-1] + $paidAmounts[$i-2]) / 3;
            }
        }

        // Add fulfillment rates (percentage of commitments paid)
        $fulfillmentRates = [];
        for ($i = 0; $i < count($committedAmounts); $i++) {
            $committedAmount = $committedAmounts[$i];
            $paidAmount = $paidAmounts[$i];
            $fulfillmentRate = ($committedAmount > 0) ? ($paidAmount / $committedAmount * 100) : 0;
            $fulfillmentRates[] = round($fulfillmentRate, 1); // Round to 1 decimal place
        }

        $data = [
            'title' => 'Dashboard',
            'totalMembers' => $totalMembers,
            'activeMembers' => $activeMembers,
            'totalCommitments' => $totalCommitments,
            'totalPayments' => $totalPayments,
            'totalOutstanding' => $totalOutstanding,
            'recentPayments' => $recentPayments,
            'membersWithOutstandingBalances' => $membersWithOutstandingBalances,
            'membersWithoutActiveCommitments' => $membersWithoutActiveCommitments,
            'startDate' => $startDate,
            'endDate' => $endDate,
            'months' => json_encode($months),
            'committedAmounts' => json_encode($committedAmounts),
            'paidAmounts' => json_encode($paidAmounts),
            'fulfillmentRates' => json_encode($fulfillmentRates),
            'movingAverages' => json_encode($movingAverages),
            'memberComplianceData' => json_encode($memberComplianceData),
            'paymentMethodData' => json_encode($paymentMethodData),
            'collectionEfficiencyData' => $collectionEfficiencyData
        ];

        return view('reports/dashboard', $data);
    }

    // Method moved to the bottom of the class to avoid duplication

    /**
     * Display the collection summary report
     *
     * @return mixed
     */
    public function collectionSummary()
    {
        // Get filter parameters from the URL
        $excludeZero = $this->request->getGet('exclude_zero') ?? 'commitments';
        $fromDate = $this->request->getGet('from_date');
        $toDate = $this->request->getGet('to_date');

        // Determine filter type
        $excludeZeroBalances = ($excludeZero === 'yes');
        $excludeZeroCommitments = ($excludeZero === 'commitments');

        // Set default date range if not provided (current year)
        if (empty($fromDate)) {
            $fromDate = date('Y-01-01'); // First day of current year
        }
        if (empty($toDate)) {
            $toDate = date('Y-m-t'); // Last day of current month
        }

        // Get summaries with member details and date filtering
        if ($fromDate && $toDate) {
            $summaries = $this->summaryModel->getAllSummariesWithMemberDetailsAndDateFilter($excludeZeroBalances, $excludeZeroCommitments, $fromDate, $toDate);
        } else {
            $summaries = $this->summaryModel->getAllSummariesWithMemberDetails($excludeZeroBalances, $excludeZeroCommitments);
        }

        // Add phone and WhatsApp numbers to the summaries
        $db = \Config\Database::connect();
        foreach ($summaries as &$summary) {
            $memberData = $db->table('members')
                ->select('phone, whatsapp_number')
                ->where('member_id', $summary['member_id'])
                ->get()
                ->getRowArray();

            $summary['phone'] = $memberData['phone'] ?? '';
            $summary['whatsapp_number'] = $memberData['whatsapp_number'] ?? '';
        }

        $data = [
            'title' => 'Collection Summary',
            'summaries' => $summaries,
            'exclude_zero' => $excludeZero,
            'from_date' => $fromDate,
            'to_date' => $toDate
        ];

        return view('reports/collection_summary', $data);
    }

    /**
     * Display the outstanding balances report
     *
     * @return mixed
     */
    public function outstandingBalances()
    {
        $data = [
            'title' => 'Outstanding Balances',
            'members' => $this->summaryModel->getMembersWithOutstandingBalances()
        ];

        return view('reports/outstanding_balances', $data);
    }

    /**
     * Display the payment history report
     *
     * @return mixed
     */
    public function paymentHistory()
    {
        $startDate = $this->request->getGet('start_date') ?? date('Y-m-d', strtotime('-1 month'));
        $endDate = $this->request->getGet('end_date') ?? date('Y-m-d');
        $memberId = $this->request->getGet('member_id') ?? null;

        $db = \Config\Database::connect();
        $builder = $db->table('payments p')
            ->select('p.*, m.name as member_name')
            ->join('members m', 'm.member_id = p.member_id')
            ->where('p.payment_date >=', $startDate)
            ->where('p.payment_date <=', $endDate)
            ->orderBy('p.payment_date', 'DESC');

        if ($memberId) {
            $builder->where('p.member_id', $memberId);
        }

        $payments = $builder->get()->getResultArray();

        $data = [
            'title' => 'Payment History',
            'payments' => $payments,
            'startDate' => $startDate,
            'endDate' => $endDate,
            'memberId' => $memberId,
            'members' => $this->memberModel->findAll(),
            'totalAmount' => array_sum(array_column($payments, 'amount'))
        ];

        return view('reports/payment_history', $data);
    }

    /**
     * Display the commitment report
     *
     * @return mixed
     */
    public function commitmentReport()
    {
        $frequency = $this->request->getGet('frequency') ?? null;
        $status = $this->request->getGet('status') ?? 'active';

        $db = \Config\Database::connect();
        $builder = $db->table('commitments c')
            ->select('c.*, m.name as member_name, m.status as member_status')
            ->join('members m', 'm.member_id = c.member_id');

        if ($frequency) {
            $builder->where('c.frequency', $frequency);
        }

        if ($status === 'active') {
            $today = date('Y-m-d');
            $builder->where('c.start_date <=', $today)
                ->groupStart()
                    ->where('c.end_date IS NULL')
                    ->orWhere('c.end_date >=', $today)
                ->groupEnd()
                ->where('m.status', 'active');
        } elseif ($status === 'inactive') {
            $today = date('Y-m-d');
            $builder->where('c.end_date <', $today)
                ->orWhere('m.status', 'inactive');
        }

        $commitments = $builder->get()->getResultArray();

        // Calculate total amount considering frequency and duration
        $totalAmount = 0;
        foreach ($commitments as &$commitment) {
            $amount = $commitment['amount'];
            $frequency = $commitment['frequency'];
            $startDate = new \DateTime($commitment['start_date']);
            $endDate = !empty($commitment['end_date']) ? new \DateTime($commitment['end_date']) : null;

            // For one-time commitments, just add the amount
            if ($frequency == 'one-time') {
                $commitment['calculated_amount'] = $amount;
                $totalAmount += $amount;
                continue;
            }

            // For ongoing commitments (no end date), calculate for 1 year from start date or today
            if ($endDate === null) {
                $endDate = new \DateTime($startDate->format('Y-m-d'));
                $endDate->modify('+1 year');
            }

            // Calculate the number of periods based on frequency
            $interval = $startDate->diff($endDate);

            // Calculate total months more accurately
            $totalMonths = ($interval->y * 12) + $interval->m;
            if ($interval->d > 0) {
                // Add one more month if there are remaining days
                $totalMonths += 1;
            }

            switch ($frequency) {
                case 'monthly':
                    $periods = max(1, $totalMonths); // Ensure at least 1 period
                    break;
                case 'quarterly':
                    $periods = max(1, ceil($totalMonths / 3)); // Ensure at least 1 period
                    break;
                case 'yearly':
                    $periods = max(1, ceil($totalMonths / 12)); // Ensure at least 1 period
                    break;
                default:
                    $periods = 1;
            }

            $calculatedAmount = $amount * $periods;
            $commitment['calculated_amount'] = $calculatedAmount;
            $commitment['periods'] = $periods;
            $totalAmount += $calculatedAmount;
        }

        $data = [
            'title' => 'Commitment Report',
            'commitments' => $commitments,
            'frequency' => $frequency,
            'status' => $status,
            'totalAmount' => $totalAmount
        ];

        return view('reports/commitment_report', $data);
    }

    /**
     * Display the statistics page with graphs
     *
     * @return mixed
     */
    public function statistics()
    {
        // Get date range from request or use default (last 12 months)
        $endDate = $this->request->getGet('end_date') ?? date('Y-m-d');
        $startDate = $this->request->getGet('start_date') ?? date('Y-m-d', strtotime('-12 months', strtotime($endDate)));

        // Convert to DateTime objects for easier manipulation
        $startDateTime = new \DateTime($startDate);
        $endDateTime = new \DateTime($endDate);

        // Ensure start date is the first day of the month
        $startDateTime->modify('first day of this month');
        $startDate = $startDateTime->format('Y-m-d');

        // Ensure end date is the last day of the month
        $endDateTime->modify('last day of this month');
        $endDate = $endDateTime->format('Y-m-d');

        // Calculate the number of months in the range
        $interval = $startDateTime->diff($endDateTime);
        $monthCount = ($interval->y * 12) + $interval->m + 1; // +1 to include both start and end months

        // Initialize arrays to store data for each month
        $months = [];
        $committedAmounts = [];
        $paidAmounts = [];

        // Get database connection
        $db = \Config\Database::connect();

        // Loop through each month in the range
        $currentDate = clone $startDateTime;
        for ($i = 0; $i < $monthCount; $i++) {
            // Format month for display (e.g., "Jan 2023")
            $monthLabel = $currentDate->format('M Y');
            $months[] = $monthLabel;

            // Get first and last day of current month
            $firstDay = $currentDate->format('Y-m-01');
            $lastDay = $currentDate->format('Y-m-t');

            // Calculate total committed amount for this month
            $committedQuery = $db->table('commitments')
                ->select('SUM(amount) as total')
                ->where('frequency', 'monthly')
                ->where('start_date <=', $lastDay)
                ->groupStart()
                    ->where('end_date IS NULL')
                    ->orWhere('end_date >=', $firstDay)
                ->groupEnd()
                ->get();

            $committedResult = $committedQuery->getRowArray();
            $committedAmounts[] = (float)($committedResult['total'] ?? 0);

            // Calculate total paid amount for this month
            $paidQuery = $db->table('payments')
                ->select('SUM(amount) as total')
                ->where('payment_date >=', $firstDay)
                ->where('payment_date <=', $lastDay)
                ->get();

            $paidResult = $paidQuery->getRowArray();
            $paidAmounts[] = (float)($paidResult['total'] ?? 0);

            // Move to next month
            $currentDate->modify('+1 month');
        }

        $data = [
            'title' => 'Statistics',
            'startDate' => $startDate,
            'endDate' => $endDate,
            'months' => json_encode($months),
            'committedAmounts' => json_encode($committedAmounts),
            'paidAmounts' => json_encode($paidAmounts)
        ];

        return view('reports/statistics', $data);
    }

    /**
     * Recalculate all collection summaries
     *
     * @return mixed
     */
    public function recalculateAllSummaries()
    {
        $members = $this->memberModel->findAll();

        foreach ($members as $member) {
            $this->summaryModel->recalculateSummary($member['member_id']);
        }

        return redirect()->to('/reports/collection-summary')
            ->with('message', 'All collection summaries have been recalculated');
    }

    /**
     * Display members without active commitments
     *
     * @return mixed
     */
    public function membersWithoutCommitments()
    {
        // Get all members without active commitments
        $membersWithoutActiveCommitments = $this->getMembersWithoutActiveCommitments();

        $data = [
            'title' => 'Inactive Commitments',
            'members' => $membersWithoutActiveCommitments
        ];

        return view('reports/members_without_commitments', $data);
    }

    /**
     * Get members without active commitments
     *
     * @return array
     */
    private function getMembersWithoutActiveCommitments()
    {
        $db = \Config\Database::connect();

        // Get current date
        $currentDate = date('Y-m-d');

        // Find active members who don't have any active commitments
        $query = $db->table('members m')
            ->select('m.*')
            ->where('m.status', 'active')
            ->whereNotIn('m.member_id', function($subquery) use ($currentDate) {
                $subquery->select('c.member_id')
                    ->from('commitments c')
                    ->where('c.start_date <=', $currentDate)
                    ->groupStart()
                        ->where('c.end_date IS NULL')
                        ->orWhere('c.end_date >=', $currentDate)
                    ->groupEnd();
            })
            ->get();

        return $query->getResultArray();
    }

    // Helper methods removed to simplify the code
}
