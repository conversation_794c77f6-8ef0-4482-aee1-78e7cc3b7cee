<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\AdminModel;
use CodeIgniter\HTTP\ResponseInterface;

class Auth extends BaseController
{
    protected $adminModel;

    public function __construct()
    {
        $this->adminModel = new AdminModel();
    }

    /**
     * Display the login page
     */
    public function index()
    {
        // If already logged in, redirect to dashboard
        if (session()->get('isLoggedIn')) {
            return redirect()->to('/reports');
        }

        return view('auth/login');
    }

    /**
     * Process the login form
     */
    public function login()
    {
        // Validate form input
        $rules = [
            'username' => 'required',
            'password' => 'required'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $username = $this->request->getPost('username');
        $password = $this->request->getPost('password');

        // Attempt to authenticate
        $user = $this->adminModel->authenticate($username, $password);

        if (!$user) {
            return redirect()->back()->withInput()->with('error', 'Invalid username or password');
        }

        // Set session data
        $userData = [
            'admin_id' => $user['admin_id'],
            'username' => $user['username'],
            'isLoggedIn' => true
        ];

        session()->set($userData);

        return redirect()->to('/reports')->with('success', 'Login successful');
    }

    /**
     * Log the user out
     */
    public function logout()
    {
        // Clear session data
        session()->destroy();

        return redirect()->to('/login')->with('success', 'You have been logged out');
    }

    /**
     * Display the password change form
     */
    public function changePassword()
    {
        // Check if user is logged in
        if (!session()->get('isLoggedIn')) {
            return redirect()->to('/login');
        }

        return view('auth/change_password');
    }

    /**
     * Process the password change form
     */
    public function updatePassword()
    {
        // Check if user is logged in
        if (!session()->get('isLoggedIn')) {
            return redirect()->to('/login');
        }

        // Validate form input
        $rules = [
            'current_password' => 'required',
            'new_password' => 'required|min_length[8]',
            'confirm_password' => 'required|matches[new_password]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->with('errors', $this->validator->getErrors());
        }

        $adminId = session()->get('admin_id');
        $currentPassword = $this->request->getPost('current_password');
        $newPassword = $this->request->getPost('new_password');

        // Attempt to change password
        $success = $this->adminModel->changePassword($adminId, $currentPassword, $newPassword);

        if (!$success) {
            return redirect()->back()->with('error', 'Current password is incorrect');
        }

        return redirect()->to('/dashboard')->with('success', 'Password changed successfully');
    }
}
