<?php

namespace App\Controllers;

use App\Models\MemberModel;
use App\Models\CommitmentModel;
use App\Models\CollectionSummaryModel;
use CodeIgniter\RESTful\ResourceController;

class Commitments extends ResourceController
{
    protected $memberModel;
    protected $commitmentModel;
    protected $summaryModel;

    public function __construct()
    {
        $this->memberModel = new MemberModel();
        $this->commitmentModel = new CommitmentModel();
        $this->summaryModel = new CollectionSummaryModel();
    }

    /**
     * Display a list of all commitments
     *
     * @return mixed
     */
    public function index()
    {
        $data = [
            'title' => 'Commitments',
            'commitments' => $this->commitmentModel->getCommitmentsWithMemberDetails()
        ];

        return view('commitments/index', $data);
    }

    /**
     * Display the form to create a new commitment
     *
     * @param int|null $memberId Optional member ID from URL parameter
     * @return mixed
     */
    public function new($memberId = null)
    {
        // Get member_id from URL parameter or query string
        if (!$memberId) {
            $memberId = $this->request->getGet('member_id');
        }

        $selectedMember = null;

        // If member_id is provided, check if the member exists
        if ($memberId) {
            $selectedMember = $this->memberModel->find($memberId);
            if (!$selectedMember) {
                return redirect()->to('/members')->with('error', 'Member not found');
            }
        }

        $data = [
            'title' => 'Add New Commitment',
            'members' => $this->memberModel->getActiveMembers(),
            'selectedMemberId' => $selectedMember ? $selectedMember['member_id'] : null
        ];

        return view('commitments/create', $data);
    }

    /**
     * Create a new commitment
     *
     * @return mixed
     */
    public function create()
    {
        $rules = $this->commitmentModel->getValidationRules();

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $memberId = $this->request->getPost('member_id');
        $startDate = $this->request->getPost('start_date');
        $endDate = $this->request->getPost('end_date') ?: null;
        $frequency = $this->request->getPost('frequency');

        // Custom validation: End date is required for monthly commitments
        if ($frequency === 'monthly' && empty($endDate)) {
            return redirect()->back()->withInput()->with('error', 'End date is required for monthly commitments.');
        }

        // If this is a monthly commitment, ensure the dates follow our convention
        if ($frequency === 'monthly') {
            // Set start date to first day of the month
            $startDateTime = new \DateTime($startDate);
            $startDateTime->modify('first day of this month');
            $startDate = $startDateTime->format('Y-m-d');

            // If end date is provided, set it to last day of the month
            if ($endDate) {
                $endDateTime = new \DateTime($endDate);
                $endDateTime->modify('last day of this month');
                $endDate = $endDateTime->format('Y-m-d');
            }
        }

        // Check if member exists
        if (!$this->memberModel->find($memberId)) {
            return redirect()->back()->withInput()->with('error', 'Member not found');
        }

        // Check for overlapping commitments
        if ($this->commitmentModel->hasOverlappingCommitments($memberId, $startDate, $endDate)) {
            $overlappingCommitments = $this->commitmentModel->getOverlappingCommitments($memberId, $startDate, $endDate);

            $errorMessage = 'This member already has commitments for the selected date range:<ul>';
            foreach ($overlappingCommitments as $commitment) {
                $errorMessage .= '<li>Amount: ' . number_format($commitment['amount'], 2) .
                                 ', Frequency: ' . ucfirst($commitment['frequency']) .
                                 ', Period: ' . date('d M Y', strtotime($commitment['start_date'])) .
                                 ' to ' . ($commitment['end_date'] ? date('d M Y', strtotime($commitment['end_date'])) : 'Ongoing') .
                                 '</li>';
            }
            $errorMessage .= '</ul>Please select a different date range.';

            return redirect()->back()->withInput()->with('error', $errorMessage);
        }

        $commitmentId = $this->commitmentModel->insert([
            'member_id' => $memberId,
            'amount' => $this->request->getPost('amount'),
            'frequency' => $this->request->getPost('frequency'),
            'start_date' => $startDate,
            'end_date' => $endDate,
            'notes' => $this->request->getPost('notes')
        ]);

        if (!$commitmentId) {
            return redirect()->back()->withInput()->with('error', 'Failed to create commitment');
        }

        // Recalculate summary
        $this->summaryModel->recalculateSummary($memberId);

        return redirect()->to('/commitments')->with('message', 'Commitment created successfully');
    }

    /**
     * Display a specific commitment
     *
     * @param int $id
     * @return mixed
     */
    public function show($id = null)
    {
        $commitment = $this->commitmentModel->find($id);

        if (!$commitment) {
            return redirect()->to('/commitments')->with('error', 'Commitment not found');
        }

        $member = $this->memberModel->find($commitment['member_id']);

        $data = [
            'title' => 'Commitment Details',
            'commitment' => $commitment,
            'member' => $member
        ];

        return view('commitments/show', $data);
    }

    /**
     * Display the form to edit a commitment
     *
     * @param int $id
     * @return mixed
     */
    public function edit($id = null)
    {
        $commitment = $this->commitmentModel->find($id);

        if (!$commitment) {
            return redirect()->to('/commitments')->with('error', 'Commitment not found');
        }

        // If this is a monthly commitment and we're editing it, ensure the dates follow our convention
        if ($commitment['frequency'] === 'monthly') {
            // Only modify the dates if they don't already follow the convention
            $startDate = new \DateTime($commitment['start_date']);
            $startDay = (int)$startDate->format('d');

            if ($startDay !== 1) {
                // Set to first day of the month
                $startDate->modify('first day of this month');
                $commitment['start_date'] = $startDate->format('Y-m-d');
            }

            if (!empty($commitment['end_date'])) {
                $endDate = new \DateTime($commitment['end_date']);
                $lastDayOfMonth = (int)date('t', strtotime($commitment['end_date']));
                $endDay = (int)$endDate->format('d');

                if ($endDay !== $lastDayOfMonth) {
                    // Set to last day of the month
                    $endDate->modify('last day of this month');
                    $commitment['end_date'] = $endDate->format('Y-m-d');
                }
            }
        }

        $data = [
            'title' => 'Edit Commitment',
            'commitment' => $commitment,
            'members' => $this->memberModel->getActiveMembers()
        ];

        return view('commitments/edit', $data);
    }

    /**
     * Update a commitment
     *
     * @param int $id
     * @return mixed
     */
    public function update($id = null)
    {
        $commitment = $this->commitmentModel->find($id);

        if (!$commitment) {
            return redirect()->to('/commitments')->with('error', 'Commitment not found');
        }

        $rules = $this->commitmentModel->getValidationRules();

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $memberId = $this->request->getPost('member_id');
        $startDate = $this->request->getPost('start_date');
        $endDate = $this->request->getPost('end_date') ?: null;
        $frequency = $this->request->getPost('frequency');

        // Custom validation: End date is required for monthly commitments
        if ($frequency === 'monthly' && empty($endDate)) {
            return redirect()->back()->withInput()->with('error', 'End date is required for monthly commitments.');
        }

        // If this is a monthly commitment, ensure the dates follow our convention
        if ($frequency === 'monthly') {
            // Set start date to first day of the month
            $startDateTime = new \DateTime($startDate);
            $startDateTime->modify('first day of this month');
            $startDate = $startDateTime->format('Y-m-d');

            // If end date is provided, set it to last day of the month
            if ($endDate) {
                $endDateTime = new \DateTime($endDate);
                $endDateTime->modify('last day of this month');
                $endDate = $endDateTime->format('Y-m-d');
            }
        }

        // Check if member exists
        if (!$this->memberModel->find($memberId)) {
            return redirect()->back()->withInput()->with('error', 'Member not found');
        }

        // Check for overlapping commitments (excluding this commitment)
        if ($this->commitmentModel->hasOverlappingCommitments($memberId, $startDate, $endDate, $id)) {
            $overlappingCommitments = $this->commitmentModel->getOverlappingCommitments($memberId, $startDate, $endDate, $id);

            $errorMessage = 'This member already has commitments for the selected date range:<ul>';
            foreach ($overlappingCommitments as $overlappingCommitment) {
                $errorMessage .= '<li>Amount: ' . number_format($overlappingCommitment['amount'], 2) .
                                 ', Frequency: ' . ucfirst($overlappingCommitment['frequency']) .
                                 ', Period: ' . date('d M Y', strtotime($overlappingCommitment['start_date'])) .
                                 ' to ' . ($overlappingCommitment['end_date'] ? date('d M Y', strtotime($overlappingCommitment['end_date'])) : 'Ongoing') .
                                 '</li>';
            }
            $errorMessage .= '</ul>Please select a different date range.';

            return redirect()->back()->withInput()->with('error', $errorMessage);
        }

        $this->commitmentModel->update($id, [
            'member_id' => $memberId,
            'amount' => $this->request->getPost('amount'),
            'frequency' => $this->request->getPost('frequency'),
            'start_date' => $startDate,
            'end_date' => $endDate,
            'notes' => $this->request->getPost('notes')
        ]);

        // Recalculate summary for both old and new member (if changed)
        $this->summaryModel->recalculateSummary($commitment['member_id']);
        if ($commitment['member_id'] != $memberId) {
            $this->summaryModel->recalculateSummary($memberId);
        }

        return redirect()->to('/commitments')->with('message', 'Commitment updated successfully');
    }

    /**
     * Delete a commitment
     *
     * @param int $id
     * @return mixed
     */
    public function delete($id = null)
    {
        $commitment = $this->commitmentModel->find($id);

        if (!$commitment) {
            return redirect()->to('/commitments')->with('error', 'Commitment not found');
        }

        $memberId = $commitment['member_id'];

        $this->commitmentModel->delete($id);

        // Recalculate summary
        $this->summaryModel->recalculateSummary($memberId);

        return redirect()->to('/commitments')->with('message', 'Commitment deleted successfully');
    }

    /**
     * Display commitments for a specific member
     *
     * @param int $memberId
     * @return mixed
     */
    public function memberCommitments($memberId)
    {
        $member = $this->memberModel->find($memberId);

        if (!$member) {
            return redirect()->to('/members')->with('error', 'Member not found');
        }

        $commitments = $this->commitmentModel->where('member_id', $memberId)
            ->orderBy('created_at', 'DESC')
            ->findAll();

        // Calculate balance for each commitment
        foreach ($commitments as &$commitment) {
            $balance = $this->commitmentModel->calculateCommitmentBalance($commitment['commitment_id']);
            $commitment['total_due'] = $balance['totalDue'];
            $commitment['total_paid'] = $balance['totalPaid'];
            $commitment['balance'] = $balance['balance'];
        }

        $data = [
            'title' => 'Commitments for ' . $member['name'],
            'member' => $member,
            'commitments' => $commitments
        ];

        return view('commitments/member_commitments', $data);
    }
}
