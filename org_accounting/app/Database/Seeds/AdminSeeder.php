<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;
use App\Models\AdminModel;

class AdminSeeder extends Seeder
{
    public function run()
    {
        $adminModel = new AdminModel();

        // Check if admin user already exists
        $existingAdmin = $adminModel->where('username', 'admin')->first();

        if (!$existingAdmin) {
            // Create admin user with the specified password
            $adminModel->insert([
                'username' => 'admin',
                'password' => 'muhammed1234#',
                'email' => '<EMAIL>',
                'last_login' => null
            ]);

            echo "Admin user created successfully.\n";
        } else {
            echo "Admin user already exists.\n";
        }
    }
}
