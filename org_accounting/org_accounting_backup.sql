-- MySQL dump 10.13  Distrib 9.3.0, for macos15.2 (arm64)
--
-- Host: localhost    Database: org_accounting
-- ------------------------------------------------------
-- Server version	9.3.0

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `admins`
--

DROP TABLE IF EXISTS `admins`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `admins` (
  `admin_id` int unsigned NOT NULL AUTO_INCREMENT,
  `username` varchar(50) COLLATE utf8mb4_general_ci NOT NULL,
  `password_hash` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `email` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `last_login` datetime DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`admin_id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `admins`
--

LOCK TABLES `admins` WRITE;
/*!40000 ALTER TABLE `admins` DISABLE KEYS */;
INSERT INTO `admins` VALUES (2,'boss','$2y$12$PLPWNSxZeAhbhm5U4uJ6Hu/mKflQD2QWf4IfnzIo5kZsgnFHpY1sG','<EMAIL>','2025-05-18 18:02:14','2025-05-18 18:46:48','2025-05-18 18:02:14');
/*!40000 ALTER TABLE `admins` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `collection_summary`
--

DROP TABLE IF EXISTS `collection_summary`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `collection_summary` (
  `summary_id` int NOT NULL AUTO_INCREMENT,
  `member_id` int NOT NULL,
  `total_committed` decimal(10,2) NOT NULL DEFAULT '0.00',
  `total_paid` decimal(10,2) NOT NULL DEFAULT '0.00',
  `balance` decimal(10,2) NOT NULL DEFAULT '0.00',
  `last_payment_date` date DEFAULT NULL,
  `last_payment_amount` decimal(10,2) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`summary_id`),
  KEY `member_id` (`member_id`),
  CONSTRAINT `collection_summary_ibfk_1` FOREIGN KEY (`member_id`) REFERENCES `members` (`member_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `collection_summary`
--

LOCK TABLES `collection_summary` WRITE;
/*!40000 ALTER TABLE `collection_summary` DISABLE KEYS */;
INSERT INTO `collection_summary` VALUES (1,1,5400.00,4600.00,800.00,'2025-05-18',1500.00,'2025-05-17 22:17:23','2025-05-18 15:15:06'),(2,2,2500.00,900.00,1600.00,'2025-05-18',900.00,'2025-05-17 23:33:08','2025-05-18 15:15:06'),(3,3,6600.00,6600.00,0.00,'2025-05-18',5000.00,'2025-05-18 00:42:32','2025-05-18 15:15:06');
/*!40000 ALTER TABLE `collection_summary` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `commitments`
--

DROP TABLE IF EXISTS `commitments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `commitments` (
  `commitment_id` int NOT NULL AUTO_INCREMENT,
  `member_id` int NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `frequency` enum('monthly','quarterly','yearly','one-time') DEFAULT 'monthly',
  `start_date` date NOT NULL,
  `end_date` date DEFAULT NULL,
  `notes` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`commitment_id`),
  KEY `member_id` (`member_id`),
  CONSTRAINT `commitments_ibfk_1` FOREIGN KEY (`member_id`) REFERENCES `members` (`member_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `commitments`
--

LOCK TABLES `commitments` WRITE;
/*!40000 ALTER TABLE `commitments` DISABLE KEYS */;
INSERT INTO `commitments` VALUES (1,1,300.00,'monthly','2018-01-01','2018-05-31','','2025-05-17 22:17:23','2025-05-17 22:18:07'),(2,1,100.00,'monthly','2018-06-01','2018-08-31','','2025-05-17 22:36:02','2025-05-17 22:42:51'),(3,1,300.00,'monthly','2018-09-01','2018-12-30','','2025-05-17 23:22:49','2025-05-17 23:22:49'),(5,2,300.00,'monthly','2020-01-01','2020-03-31','','2025-05-18 00:17:36','2025-05-18 00:18:24'),(6,1,400.00,'monthly','2020-01-01','2020-03-31','','2025-05-18 00:19:45','2025-05-18 00:19:45'),(7,3,5000.00,'one-time','2025-02-01','2025-06-30','','2025-05-18 00:42:32','2025-05-18 00:42:32'),(8,3,400.00,'monthly','2024-04-01','2024-07-31','','2025-05-18 01:23:53','2025-05-18 01:23:53'),(9,1,200.00,'monthly','2021-10-01','2022-01-31','','2025-05-18 12:44:22','2025-05-18 12:44:22'),(10,1,100.00,'monthly','2015-01-01','2015-04-30','','2025-05-18 15:00:50','2025-05-18 15:00:50'),(11,2,400.00,'monthly','2016-01-01','2016-04-30','','2025-05-18 15:06:12','2025-05-18 15:06:12');
/*!40000 ALTER TABLE `commitments` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `members`
--

DROP TABLE IF EXISTS `members`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `members` (
  `member_id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `post_office` varchar(100) DEFAULT NULL,
  `old_reference` varchar(100) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `address` text,
  `join_date` date NOT NULL,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`member_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `members`
--

LOCK TABLES `members` WRITE;
/*!40000 ALTER TABLE `members` DISABLE KEYS */;
INSERT INTO `members` VALUES (1,'Abdu Raheem KK','<EMAIL>','Karalmanna',NULL,'09947777125','Noor Mahal, Pottathiparamb, Karalmanna P O\r\nKaralmanna\r\nCCST college road','2018-01-01','active','2025-05-17 22:16:26','2025-05-18 14:10:41'),(2,'Subeena','<EMAIL>',NULL,NULL,'9947777124','Noor Mahal, Pottathiparamb, Karalmanna P O\r\nKaralmanna\r\nCCST college road','2025-05-18','active','2025-05-17 23:25:30','2025-05-17 23:25:30'),(3,'Murad','<EMAIL>','Karalmanna',NULL,'996677889','Nellaea','2025-04-08','active','2025-05-18 00:41:57','2025-05-18 14:00:36');
/*!40000 ALTER TABLE `members` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `migrations`
--

DROP TABLE IF EXISTS `migrations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `migrations` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `version` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `class` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `group` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `namespace` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `time` int NOT NULL,
  `batch` int unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `migrations`
--

LOCK TABLES `migrations` WRITE;
/*!40000 ALTER TABLE `migrations` DISABLE KEYS */;
INSERT INTO `migrations` VALUES (1,'2025-05-18-130339','App\\Database\\Migrations\\CreateAdminsTable','default','App',1747573846,1),(2,'2025-05-18-132730','App\\Database\\Migrations\\AddPaymentPeriodsToPayments','default','App',1747574959,2);
/*!40000 ALTER TABLE `migrations` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `payments`
--

DROP TABLE IF EXISTS `payments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `payments` (
  `payment_id` int NOT NULL AUTO_INCREMENT,
  `member_id` int NOT NULL,
  `commitment_id` int DEFAULT NULL,
  `amount` decimal(10,2) NOT NULL,
  `payment_date` date NOT NULL,
  `receipt_number` varchar(50) DEFAULT NULL,
  `receipt_book_number` varchar(50) DEFAULT NULL,
  `payment_method` enum('cash','check','bank_transfer','other') DEFAULT 'cash',
  `notes` text,
  `payment_periods` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`payment_id`),
  UNIQUE KEY `receipt_number` (`receipt_number`),
  KEY `member_id` (`member_id`),
  KEY `commitment_id` (`commitment_id`),
  CONSTRAINT `payments_ibfk_1` FOREIGN KEY (`member_id`) REFERENCES `members` (`member_id`) ON DELETE CASCADE,
  CONSTRAINT `payments_ibfk_2` FOREIGN KEY (`commitment_id`) REFERENCES `commitments` (`commitment_id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `payments`
--

LOCK TABLES `payments` WRITE;
/*!40000 ALTER TABLE `payments` DISABLE KEYS */;
INSERT INTO `payments` VALUES (1,1,1,1500.00,'2025-05-18','RCPT2025050001',NULL,'cash','Payment for: January 2018, February 2018, March 2018, April 2018, May 2018',NULL,'2025-05-17 22:34:00','2025-05-17 22:34:00'),(2,1,2,300.00,'2025-05-18','RCPT2025050002',NULL,'check','Payment for: June 2018, July 2018, August 2018',NULL,'2025-05-17 22:55:51','2025-05-17 22:55:51'),(4,1,3,1200.00,'2025-05-18','RCPT2025050010',NULL,'check','Payment for: September 2018, October 2018, November 2018, December 2018',NULL,'2025-05-18 00:10:35','2025-05-18 00:10:35'),(5,2,5,900.00,'2025-05-18','RCPT2025050012',NULL,'check','',NULL,'2025-05-18 00:21:07','2025-05-18 13:00:11'),(6,3,7,5000.00,'2025-05-18','RCPT2025050013',NULL,'other','',NULL,'2025-05-18 00:43:02','2025-05-18 00:43:02'),(7,3,8,1600.00,'2025-05-18','RCPT2025050014',NULL,'cash','Payment for: April 2024, May 2024, June 2024, July 2024',NULL,'2025-05-18 01:24:40','2025-05-18 01:24:40'),(8,1,6,1200.00,'2025-05-18','RCPT2025050015','1','check','Payment for: January 2020, February 2020, March 2020',NULL,'2025-05-18 07:32:08','2025-05-18 07:32:08'),(9,1,9,400.00,'2025-05-18','RCPT2025050016','1','check','Payment for: October 2021, November 2021','[\"2021-10\",\"2021-11\"]','2025-05-18 12:46:23','2025-05-18 12:46:23');
/*!40000 ALTER TABLE `payments` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-05-19  3:04:32
