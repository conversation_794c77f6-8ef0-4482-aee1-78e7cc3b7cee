# Organization Accounting System Setup Instructions

## Prerequisites

Before setting up this project, you need to have the following installed:

1. PHP (version 7.4 or higher)
2. MySQL (version 5.7 or higher)
3. Composer (for PHP dependency management)
4. Web server (Apache or Nginx)

## Installation Steps

### 1. Install PHP and MySQL

#### For macOS:
```bash
# Using Homebrew
brew install php
brew install mysql
brew services start mysql
```

#### For Ubuntu/Debian:
```bash
sudo apt update
sudo apt install php php-cli php-fpm php-json php-common php-mysql php-zip php-gd php-mbstring php-curl php-xml php-pear php-bcmath
sudo apt install mysql-server
sudo systemctl start mysql
```

### 2. Set up MySQL

```bash
# Log in to MySQL (you might need to use sudo on Linux)
mysql -u root -p

# Then in the MySQL prompt, create the database:
CREATE DATABASE org_accounting;
CREATE USER 'org_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON org_accounting.* TO 'org_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### 3. Install Composer

#### For macOS:
```bash
brew install composer
```

#### For Ubuntu/Debian:
```bash
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer
sudo chmod +x /usr/local/bin/composer
```

### 4. Install CodeIgniter

```bash
# Create a new CodeIgniter project
composer create-project codeigniter4/appstarter org_accounting
cd org_accounting
```

### 5. Configure the Database

Edit the `.env` file in the project root and set the database configuration:

```
database.default.hostname = localhost
database.default.database = org_accounting
database.default.username = org_user
database.default.password = your_password
database.default.DBDriver = MySQLi
```

### 6. Import the Database Schema

```bash
mysql -u org_user -p org_accounting < /path/to/database_schema.sql
```

### 7. Start the Development Server

```bash
# Navigate to the project directory
cd org_accounting

# Start the CodeIgniter development server
php spark serve
```

The application should now be running at http://localhost:8080

## Project Structure

The CodeIgniter project follows the MVC (Model-View-Controller) pattern:

- **Models**: Located in `app/Models/`
- **Views**: Located in `app/Views/`
- **Controllers**: Located in `app/Controllers/`

## Next Steps

1. Access the application at http://localhost:8080
2. Create members, commitments, and record payments
3. View reports and collection summaries
