# Organization Accounting System

A web-based accounting system built with CodeIgniter 4 and MySQL for managing member commitments and payments in a small organization.

## Features

- **Member Management**: Add, edit, and manage organization members
- **Commitment Tracking**: Record and track financial commitments made by members
- **Payment Processing**: Record payments and generate receipts
- **Collection Summary**: View summary of commitments, payments, and outstanding balances
- **Reports**: Generate various reports including outstanding balances and payment history

## System Requirements

- PHP 7.4 or higher
- MySQL 5.7 or higher
- Composer (for PHP dependency management)
- Web server (Apache or Nginx)

## Installation

1. **Clone the repository**

```bash
git clone <repository-url>
cd organization-accounting-system
```

2. **Install dependencies**

```bash
composer install
```

3. **Configure the database**

Copy the `.env.example` file to `.env` and update the database settings:

```
database.default.hostname = localhost
database.default.database = org_accounting
database.default.username = your_username
database.default.password = your_password
database.default.DBDriver = MySQLi
```

4. **Create the database**

```bash
mysql -u root -p
```

```sql
CREATE DATABASE org_accounting;
CREATE USER 'your_username'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON org_accounting.* TO 'your_username'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

5. **Import the database schema**

```bash
mysql -u your_username -p org_accounting < database_schema.sql
```

6. **Start the development server**

```bash
php spark serve
```

The application should now be running at http://localhost:8080

## Usage

### Dashboard

The dashboard provides an overview of the organization's financial status, including:
- Total members
- Total commitments
- Total payments
- Outstanding balances
- Recent payments
- Members with outstanding balances

### Members

- **Add Member**: Create new member profiles with contact information
- **View Members**: See a list of all members and their status
- **Member Details**: View detailed information about each member, including their commitments and payment history

### Commitments

- **Add Commitment**: Record financial commitments made by members
- **View Commitments**: See a list of all commitments
- **Commitment Details**: View detailed information about each commitment

### Payments

- **Record Payment**: Record payments made by members
- **Generate Receipt**: Generate printable receipts for payments
- **Payment History**: View payment history for each member

### Reports

- **Collection Summary**: View summary of commitments, payments, and outstanding balances for all members
- **Outstanding Balances**: View members with outstanding balances
- **Payment History**: View payment history for a specific period
- **Commitment Report**: View commitments based on frequency and status

## Database Structure

The system uses the following database tables:

- **members**: Stores member information
- **commitments**: Records financial commitments made by members
- **payments**: Tracks payments made by members
- **collection_summary**: Maintains a summary of commitments, payments, and balances

## Customization

You can customize the organization information in the `Payments` controller's `generateReceipt` method:

```php
$data = [
    // ...
    'organization' => [
        'name' => 'Your Organization Name',
        'address' => 'Your Organization Address',
        'phone' => 'Your Organization Phone',
        'email' => 'Your Organization Email'
    ]
];
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.
