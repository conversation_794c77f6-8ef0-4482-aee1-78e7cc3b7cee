<?php

namespace App\Controllers;

use App\Models\MemberModel;
use App\Models\CommitmentModel;
use App\Models\CollectionSummaryModel;
use CodeIgniter\RESTful\ResourceController;

class Commitments extends ResourceController
{
    protected $memberModel;
    protected $commitmentModel;
    protected $summaryModel;
    
    public function __construct()
    {
        $this->memberModel = new MemberModel();
        $this->commitmentModel = new CommitmentModel();
        $this->summaryModel = new CollectionSummaryModel();
    }
    
    /**
     * Display a list of all commitments
     *
     * @return mixed
     */
    public function index()
    {
        $data = [
            'title' => 'Commitments',
            'commitments' => $this->commitmentModel->getCommitmentsWithMemberDetails()
        ];
        
        return view('commitments/index', $data);
    }
    
    /**
     * Display the form to create a new commitment
     *
     * @return mixed
     */
    public function new()
    {
        $data = [
            'title' => 'Add New Commitment',
            'members' => $this->memberModel->getActiveMembers()
        ];
        
        return view('commitments/create', $data);
    }
    
    /**
     * Create a new commitment
     *
     * @return mixed
     */
    public function create()
    {
        $rules = $this->commitmentModel->getValidationRules();
        
        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }
        
        $memberId = $this->request->getPost('member_id');
        
        // Check if member exists
        if (!$this->memberModel->find($memberId)) {
            return redirect()->back()->withInput()->with('error', 'Member not found');
        }
        
        $commitmentId = $this->commitmentModel->insert([
            'member_id' => $memberId,
            'amount' => $this->request->getPost('amount'),
            'frequency' => $this->request->getPost('frequency'),
            'start_date' => $this->request->getPost('start_date'),
            'end_date' => $this->request->getPost('end_date') ?: null,
            'notes' => $this->request->getPost('notes')
        ]);
        
        if (!$commitmentId) {
            return redirect()->back()->withInput()->with('error', 'Failed to create commitment');
        }
        
        // Recalculate summary
        $this->summaryModel->recalculateSummary($memberId);
        
        return redirect()->to('/commitments')->with('message', 'Commitment created successfully');
    }
    
    /**
     * Display a specific commitment
     *
     * @param int $id
     * @return mixed
     */
    public function show($id = null)
    {
        $commitment = $this->commitmentModel->find($id);
        
        if (!$commitment) {
            return redirect()->to('/commitments')->with('error', 'Commitment not found');
        }
        
        $member = $this->memberModel->find($commitment['member_id']);
        
        $data = [
            'title' => 'Commitment Details',
            'commitment' => $commitment,
            'member' => $member
        ];
        
        return view('commitments/show', $data);
    }
    
    /**
     * Display the form to edit a commitment
     *
     * @param int $id
     * @return mixed
     */
    public function edit($id = null)
    {
        $commitment = $this->commitmentModel->find($id);
        
        if (!$commitment) {
            return redirect()->to('/commitments')->with('error', 'Commitment not found');
        }
        
        $data = [
            'title' => 'Edit Commitment',
            'commitment' => $commitment,
            'members' => $this->memberModel->getActiveMembers()
        ];
        
        return view('commitments/edit', $data);
    }
    
    /**
     * Update a commitment
     *
     * @param int $id
     * @return mixed
     */
    public function update($id = null)
    {
        $commitment = $this->commitmentModel->find($id);
        
        if (!$commitment) {
            return redirect()->to('/commitments')->with('error', 'Commitment not found');
        }
        
        $rules = $this->commitmentModel->getValidationRules();
        
        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }
        
        $memberId = $this->request->getPost('member_id');
        
        // Check if member exists
        if (!$this->memberModel->find($memberId)) {
            return redirect()->back()->withInput()->with('error', 'Member not found');
        }
        
        $this->commitmentModel->update($id, [
            'member_id' => $memberId,
            'amount' => $this->request->getPost('amount'),
            'frequency' => $this->request->getPost('frequency'),
            'start_date' => $this->request->getPost('start_date'),
            'end_date' => $this->request->getPost('end_date') ?: null,
            'notes' => $this->request->getPost('notes')
        ]);
        
        // Recalculate summary for both old and new member (if changed)
        $this->summaryModel->recalculateSummary($commitment['member_id']);
        if ($commitment['member_id'] != $memberId) {
            $this->summaryModel->recalculateSummary($memberId);
        }
        
        return redirect()->to('/commitments')->with('message', 'Commitment updated successfully');
    }
    
    /**
     * Delete a commitment
     *
     * @param int $id
     * @return mixed
     */
    public function delete($id = null)
    {
        $commitment = $this->commitmentModel->find($id);
        
        if (!$commitment) {
            return redirect()->to('/commitments')->with('error', 'Commitment not found');
        }
        
        $memberId = $commitment['member_id'];
        
        $this->commitmentModel->delete($id);
        
        // Recalculate summary
        $this->summaryModel->recalculateSummary($memberId);
        
        return redirect()->to('/commitments')->with('message', 'Commitment deleted successfully');
    }
    
    /**
     * Display commitments for a specific member
     *
     * @param int $memberId
     * @return mixed
     */
    public function memberCommitments($memberId)
    {
        $member = $this->memberModel->find($memberId);
        
        if (!$member) {
            return redirect()->to('/members')->with('error', 'Member not found');
        }
        
        $data = [
            'title' => 'Commitments for ' . $member['name'],
            'member' => $member,
            'commitments' => $this->commitmentModel->where('member_id', $memberId)->findAll()
        ];
        
        return view('commitments/member_commitments', $data);
    }
}
