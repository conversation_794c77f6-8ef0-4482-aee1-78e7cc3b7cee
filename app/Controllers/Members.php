<?php

namespace App\Controllers;

use App\Models\MemberModel;
use App\Models\CommitmentModel;
use App\Models\PaymentModel;
use App\Models\CollectionSummaryModel;
use CodeIgniter\RESTful\ResourceController;

class Members extends ResourceController
{
    protected $memberModel;
    protected $commitmentModel;
    protected $paymentModel;
    protected $summaryModel;
    
    public function __construct()
    {
        $this->memberModel = new MemberModel();
        $this->commitmentModel = new CommitmentModel();
        $this->paymentModel = new PaymentModel();
        $this->summaryModel = new CollectionSummaryModel();
    }
    
    /**
     * Display a list of all members
     *
     * @return mixed
     */
    public function index()
    {
        $data = [
            'title' => 'Members',
            'members' => $this->memberModel->findAll()
        ];
        
        return view('members/index', $data);
    }
    
    /**
     * Display the form to create a new member
     *
     * @return mixed
     */
    public function new()
    {
        $data = [
            'title' => 'Add New Member'
        ];
        
        return view('members/create', $data);
    }
    
    /**
     * Create a new member
     *
     * @return mixed
     */
    public function create()
    {
        $rules = $this->memberModel->getValidationRules();
        
        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }
        
        $memberId = $this->memberModel->insert([
            'name' => $this->request->getPost('name'),
            'email' => $this->request->getPost('email'),
            'phone' => $this->request->getPost('phone'),
            'address' => $this->request->getPost('address'),
            'join_date' => $this->request->getPost('join_date'),
            'status' => 'active'
        ]);
        
        if (!$memberId) {
            return redirect()->back()->withInput()->with('error', 'Failed to create member');
        }
        
        return redirect()->to('/members')->with('message', 'Member created successfully');
    }
    
    /**
     * Display a specific member
     *
     * @param int $id
     * @return mixed
     */
    public function show($id = null)
    {
        $member = $this->memberModel->getMemberWithDetails($id);
        
        if (!$member) {
            return redirect()->to('/members')->with('error', 'Member not found');
        }
        
        $data = [
            'title' => 'Member Details: ' . $member['name'],
            'member' => $member
        ];
        
        return view('members/show', $data);
    }
    
    /**
     * Display the form to edit a member
     *
     * @param int $id
     * @return mixed
     */
    public function edit($id = null)
    {
        $member = $this->memberModel->find($id);
        
        if (!$member) {
            return redirect()->to('/members')->with('error', 'Member not found');
        }
        
        $data = [
            'title' => 'Edit Member: ' . $member['name'],
            'member' => $member
        ];
        
        return view('members/edit', $data);
    }
    
    /**
     * Update a member
     *
     * @param int $id
     * @return mixed
     */
    public function update($id = null)
    {
        $member = $this->memberModel->find($id);
        
        if (!$member) {
            return redirect()->to('/members')->with('error', 'Member not found');
        }
        
        $rules = $this->memberModel->getValidationRules();
        
        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }
        
        $this->memberModel->update($id, [
            'name' => $this->request->getPost('name'),
            'email' => $this->request->getPost('email'),
            'phone' => $this->request->getPost('phone'),
            'address' => $this->request->getPost('address'),
            'join_date' => $this->request->getPost('join_date'),
            'status' => $this->request->getPost('status')
        ]);
        
        return redirect()->to('/members')->with('message', 'Member updated successfully');
    }
    
    /**
     * Delete a member
     *
     * @param int $id
     * @return mixed
     */
    public function delete($id = null)
    {
        $member = $this->memberModel->find($id);
        
        if (!$member) {
            return redirect()->to('/members')->with('error', 'Member not found');
        }
        
        $this->memberModel->delete($id);
        
        return redirect()->to('/members')->with('message', 'Member deleted successfully');
    }
}
