<?php

namespace App\Controllers;

use App\Models\MemberModel;
use App\Models\CommitmentModel;
use App\Models\PaymentModel;
use App\Models\CollectionSummaryModel;
use CodeIgniter\RESTful\ResourceController;

class Payments extends ResourceController
{
    protected $memberModel;
    protected $commitmentModel;
    protected $paymentModel;
    protected $summaryModel;
    
    public function __construct()
    {
        $this->memberModel = new MemberModel();
        $this->commitmentModel = new CommitmentModel();
        $this->paymentModel = new PaymentModel();
        $this->summaryModel = new CollectionSummaryModel();
    }
    
    /**
     * Display a list of all payments
     *
     * @return mixed
     */
    public function index()
    {
        $data = [
            'title' => 'Payments',
            'payments' => $this->paymentModel->getPaymentsWithMemberDetails()
        ];
        
        return view('payments/index', $data);
    }
    
    /**
     * Display the form to create a new payment
     *
     * @return mixed
     */
    public function new()
    {
        $data = [
            'title' => 'Add New Payment',
            'members' => $this->memberModel->getActiveMembers(),
            'receipt_number' => $this->paymentModel->generateReceiptNumber()
        ];
        
        return view('payments/create', $data);
    }
    
    /**
     * Create a new payment
     *
     * @return mixed
     */
    public function create()
    {
        $rules = $this->paymentModel->getValidationRules();
        
        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }
        
        $memberId = $this->request->getPost('member_id');
        $commitmentId = $this->request->getPost('commitment_id') ?: null;
        
        // Check if member exists
        if (!$this->memberModel->find($memberId)) {
            return redirect()->back()->withInput()->with('error', 'Member not found');
        }
        
        // Check if commitment exists if provided
        if ($commitmentId && !$this->commitmentModel->find($commitmentId)) {
            return redirect()->back()->withInput()->with('error', 'Commitment not found');
        }
        
        $paymentId = $this->paymentModel->insert([
            'member_id' => $memberId,
            'commitment_id' => $commitmentId,
            'amount' => $this->request->getPost('amount'),
            'payment_date' => $this->request->getPost('payment_date'),
            'receipt_number' => $this->request->getPost('receipt_number'),
            'payment_method' => $this->request->getPost('payment_method'),
            'notes' => $this->request->getPost('notes')
        ]);
        
        if (!$paymentId) {
            return redirect()->back()->withInput()->with('error', 'Failed to create payment');
        }
        
        // Recalculate summary
        $this->summaryModel->recalculateSummary($memberId);
        
        return redirect()->to('/payments')->with('message', 'Payment recorded successfully');
    }
    
    /**
     * Display a specific payment
     *
     * @param int $id
     * @return mixed
     */
    public function show($id = null)
    {
        $payment = $this->paymentModel->find($id);
        
        if (!$payment) {
            return redirect()->to('/payments')->with('error', 'Payment not found');
        }
        
        $member = $this->memberModel->find($payment['member_id']);
        $commitment = null;
        
        if ($payment['commitment_id']) {
            $commitment = $this->commitmentModel->find($payment['commitment_id']);
        }
        
        $data = [
            'title' => 'Payment Details',
            'payment' => $payment,
            'member' => $member,
            'commitment' => $commitment
        ];
        
        return view('payments/show', $data);
    }
    
    /**
     * Display the form to edit a payment
     *
     * @param int $id
     * @return mixed
     */
    public function edit($id = null)
    {
        $payment = $this->paymentModel->find($id);
        
        if (!$payment) {
            return redirect()->to('/payments')->with('error', 'Payment not found');
        }
        
        $data = [
            'title' => 'Edit Payment',
            'payment' => $payment,
            'members' => $this->memberModel->getActiveMembers(),
            'commitments' => $this->commitmentModel->where('member_id', $payment['member_id'])->findAll()
        ];
        
        return view('payments/edit', $data);
    }
    
    /**
     * Update a payment
     *
     * @param int $id
     * @return mixed
     */
    public function update($id = null)
    {
        $payment = $this->paymentModel->find($id);
        
        if (!$payment) {
            return redirect()->to('/payments')->with('error', 'Payment not found');
        }
        
        $rules = $this->paymentModel->getValidationRules();
        
        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }
        
        $memberId = $this->request->getPost('member_id');
        $commitmentId = $this->request->getPost('commitment_id') ?: null;
        
        // Check if member exists
        if (!$this->memberModel->find($memberId)) {
            return redirect()->back()->withInput()->with('error', 'Member not found');
        }
        
        // Check if commitment exists if provided
        if ($commitmentId && !$this->commitmentModel->find($commitmentId)) {
            return redirect()->back()->withInput()->with('error', 'Commitment not found');
        }
        
        $this->paymentModel->update($id, [
            'member_id' => $memberId,
            'commitment_id' => $commitmentId,
            'amount' => $this->request->getPost('amount'),
            'payment_date' => $this->request->getPost('payment_date'),
            'receipt_number' => $this->request->getPost('receipt_number'),
            'payment_method' => $this->request->getPost('payment_method'),
            'notes' => $this->request->getPost('notes')
        ]);
        
        // Recalculate summary for both old and new member (if changed)
        $this->summaryModel->recalculateSummary($payment['member_id']);
        if ($payment['member_id'] != $memberId) {
            $this->summaryModel->recalculateSummary($memberId);
        }
        
        return redirect()->to('/payments')->with('message', 'Payment updated successfully');
    }
    
    /**
     * Delete a payment
     *
     * @param int $id
     * @return mixed
     */
    public function delete($id = null)
    {
        $payment = $this->paymentModel->find($id);
        
        if (!$payment) {
            return redirect()->to('/payments')->with('error', 'Payment not found');
        }
        
        $memberId = $payment['member_id'];
        
        $this->paymentModel->delete($id);
        
        // Recalculate summary
        $this->summaryModel->recalculateSummary($memberId);
        
        return redirect()->to('/payments')->with('message', 'Payment deleted successfully');
    }
    
    /**
     * Display payments for a specific member
     *
     * @param int $memberId
     * @return mixed
     */
    public function memberPayments($memberId)
    {
        $member = $this->memberModel->find($memberId);
        
        if (!$member) {
            return redirect()->to('/members')->with('error', 'Member not found');
        }
        
        $data = [
            'title' => 'Payments for ' . $member['name'],
            'member' => $member,
            'payments' => $this->paymentModel->getMemberPayments($memberId)
        ];
        
        return view('payments/member_payments', $data);
    }
    
    /**
     * Generate a receipt for a payment
     *
     * @param int $id
     * @return mixed
     */
    public function generateReceipt($id = null)
    {
        $payment = $this->paymentModel->find($id);
        
        if (!$payment) {
            return redirect()->to('/payments')->with('error', 'Payment not found');
        }
        
        $member = $this->memberModel->find($payment['member_id']);
        $commitment = null;
        
        if ($payment['commitment_id']) {
            $commitment = $this->commitmentModel->find($payment['commitment_id']);
        }
        
        $data = [
            'title' => 'Receipt',
            'payment' => $payment,
            'member' => $member,
            'commitment' => $commitment,
            'organization' => [
                'name' => 'Your Organization Name',
                'address' => 'Your Organization Address',
                'phone' => 'Your Organization Phone',
                'email' => 'Your Organization Email'
            ]
        ];
        
        return view('payments/receipt', $data);
    }
}
