<?php

namespace App\Controllers;

use App\Models\MemberModel;
use App\Models\CommitmentModel;
use App\Models\PaymentModel;
use App\Models\CollectionSummaryModel;
use CodeIgniter\Controller;

class Reports extends Controller
{
    protected $memberModel;
    protected $commitmentModel;
    protected $paymentModel;
    protected $summaryModel;
    
    public function __construct()
    {
        $this->memberModel = new MemberModel();
        $this->commitmentModel = new CommitmentModel();
        $this->paymentModel = new PaymentModel();
        $this->summaryModel = new CollectionSummaryModel();
    }
    
    /**
     * Display the dashboard with summary reports
     *
     * @return mixed
     */
    public function index()
    {
        $db = \Config\Database::connect();
        
        // Get total members
        $totalMembers = $this->memberModel->countAllResults();
        $activeMembers = $this->memberModel->where('status', 'active')->countAllResults();
        
        // Get total commitments
        $totalCommitments = $this->commitmentModel->selectSum('amount')->first()['amount'] ?? 0;
        
        // Get total payments
        $totalPayments = $this->paymentModel->selectSum('amount')->first()['amount'] ?? 0;
        
        // Get total outstanding balance
        $totalOutstanding = $this->summaryModel->selectSum('balance')->first()['balance'] ?? 0;
        
        // Get recent payments
        $recentPayments = $this->paymentModel->getPaymentsWithMemberDetails();
        $recentPayments = array_slice($recentPayments, 0, 5);
        
        // Get members with outstanding balances
        $membersWithOutstandingBalances = $this->summaryModel->getMembersWithOutstandingBalances();
        $membersWithOutstandingBalances = array_slice($membersWithOutstandingBalances, 0, 5);
        
        $data = [
            'title' => 'Dashboard',
            'totalMembers' => $totalMembers,
            'activeMembers' => $activeMembers,
            'totalCommitments' => $totalCommitments,
            'totalPayments' => $totalPayments,
            'totalOutstanding' => $totalOutstanding,
            'recentPayments' => $recentPayments,
            'membersWithOutstandingBalances' => $membersWithOutstandingBalances
        ];
        
        return view('reports/dashboard', $data);
    }
    
    /**
     * Display the collection summary report
     *
     * @return mixed
     */
    public function collectionSummary()
    {
        $data = [
            'title' => 'Collection Summary',
            'summaries' => $this->summaryModel->getAllSummariesWithMemberDetails()
        ];
        
        return view('reports/collection_summary', $data);
    }
    
    /**
     * Display the outstanding balances report
     *
     * @return mixed
     */
    public function outstandingBalances()
    {
        $data = [
            'title' => 'Outstanding Balances',
            'members' => $this->summaryModel->getMembersWithOutstandingBalances()
        ];
        
        return view('reports/outstanding_balances', $data);
    }
    
    /**
     * Display the payment history report
     *
     * @return mixed
     */
    public function paymentHistory()
    {
        $startDate = $this->request->getGet('start_date') ?? date('Y-m-d', strtotime('-1 month'));
        $endDate = $this->request->getGet('end_date') ?? date('Y-m-d');
        $memberId = $this->request->getGet('member_id') ?? null;
        
        $db = \Config\Database::connect();
        $builder = $db->table('payments p')
            ->select('p.*, m.name as member_name')
            ->join('members m', 'm.member_id = p.member_id')
            ->where('p.payment_date >=', $startDate)
            ->where('p.payment_date <=', $endDate)
            ->orderBy('p.payment_date', 'DESC');
            
        if ($memberId) {
            $builder->where('p.member_id', $memberId);
        }
        
        $payments = $builder->get()->getResultArray();
        
        $data = [
            'title' => 'Payment History',
            'payments' => $payments,
            'startDate' => $startDate,
            'endDate' => $endDate,
            'memberId' => $memberId,
            'members' => $this->memberModel->findAll(),
            'totalAmount' => array_sum(array_column($payments, 'amount'))
        ];
        
        return view('reports/payment_history', $data);
    }
    
    /**
     * Display the commitment report
     *
     * @return mixed
     */
    public function commitmentReport()
    {
        $frequency = $this->request->getGet('frequency') ?? null;
        $status = $this->request->getGet('status') ?? 'active';
        
        $db = \Config\Database::connect();
        $builder = $db->table('commitments c')
            ->select('c.*, m.name as member_name, m.status as member_status')
            ->join('members m', 'm.member_id = c.member_id');
            
        if ($frequency) {
            $builder->where('c.frequency', $frequency);
        }
        
        if ($status === 'active') {
            $today = date('Y-m-d');
            $builder->where('c.start_date <=', $today)
                ->groupStart()
                    ->where('c.end_date IS NULL')
                    ->orWhere('c.end_date >=', $today)
                ->groupEnd()
                ->where('m.status', 'active');
        } elseif ($status === 'inactive') {
            $today = date('Y-m-d');
            $builder->where('c.end_date <', $today)
                ->orWhere('m.status', 'inactive');
        }
        
        $commitments = $builder->get()->getResultArray();
        
        $data = [
            'title' => 'Commitment Report',
            'commitments' => $commitments,
            'frequency' => $frequency,
            'status' => $status,
            'totalAmount' => array_sum(array_column($commitments, 'amount'))
        ];
        
        return view('reports/commitment_report', $data);
    }
    
    /**
     * Recalculate all collection summaries
     *
     * @return mixed
     */
    public function recalculateAllSummaries()
    {
        $members = $this->memberModel->findAll();
        
        foreach ($members as $member) {
            $this->summaryModel->recalculateSummary($member['member_id']);
        }
        
        return redirect()->to('/reports/collection-summary')
            ->with('message', 'All collection summaries have been recalculated');
    }
}
