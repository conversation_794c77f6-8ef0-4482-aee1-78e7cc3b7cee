<?php

namespace App\Controllers;

use App\Models\CommitmentModel;
use CodeIgniter\RESTful\ResourceController;

class Api extends ResourceController
{
    protected $format = 'json';
    
    /**
     * Get commitments for a specific member
     *
     * @param int $memberId
     * @return mixed
     */
    public function getMemberCommitments($memberId)
    {
        $commitmentModel = new CommitmentModel();
        $commitments = $commitmentModel->where('member_id', $memberId)->findAll();
        
        return $this->respond($commitments);
    }
}
