<?php

namespace App\Models;

use CodeIgniter\Model;

class CommitmentModel extends Model
{
    protected $table = 'commitments';
    protected $primaryKey = 'commitment_id';
    
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    
    protected $allowedFields = [
        'member_id', 
        'amount', 
        'frequency', 
        'start_date', 
        'end_date', 
        'notes'
    ];
    
    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    
    protected $validationRules = [
        'member_id' => 'required|integer',
        'amount' => 'required|numeric',
        'frequency' => 'required|in_list[monthly,quarterly,yearly,one-time]',
        'start_date' => 'required|valid_date',
        'end_date' => 'permit_empty|valid_date'
    ];
    
    protected $validationMessages = [
        'member_id' => [
            'required' => 'Member ID is required',
            'integer' => 'Member ID must be an integer'
        ],
        'amount' => [
            'required' => 'Amount is required',
            'numeric' => 'Amount must be a number'
        ],
        'frequency' => [
            'required' => 'Frequency is required',
            'in_list' => 'Frequency must be one of: monthly, quarterly, yearly, one-time'
        ],
        'start_date' => [
            'required' => 'Start date is required',
            'valid_date' => 'Start date must be a valid date'
        ],
        'end_date' => [
            'valid_date' => 'End date must be a valid date'
        ]
    ];
    
    protected $skipValidation = false;
    
    /**
     * Get active commitments for a member
     *
     * @param int $memberId
     * @return array
     */
    public function getActiveCommitments($memberId)
    {
        $today = date('Y-m-d');
        
        return $this->where('member_id', $memberId)
            ->where('start_date <=', $today)
            ->where('(end_date IS NULL OR end_date >=', $today . ')')
            ->findAll();
    }
    
    /**
     * Calculate total commitment amount for a member
     *
     * @param int $memberId
     * @return float
     */
    public function calculateTotalCommitment($memberId)
    {
        $result = $this->selectSum('amount')
            ->where('member_id', $memberId)
            ->first();
            
        return $result['amount'] ?? 0;
    }
    
    /**
     * Get commitments with member details
     *
     * @return array
     */
    public function getCommitmentsWithMemberDetails()
    {
        $db = \Config\Database::connect();
        
        $query = $db->table('commitments c')
            ->select('c.*, m.name as member_name')
            ->join('members m', 'm.member_id = c.member_id')
            ->get();
            
        return $query->getResultArray();
    }
}
