<?= $this->extend('layout/main') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Members</h1>
    <a href="<?= site_url('members/new') ?>" class="btn btn-primary">
        <i class="fas fa-plus"></i> Add New Member
    </a>
</div>

<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover datatable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Name</th>
                        <th>Email</th>
                        <th>Phone</th>
                        <th>Join Date</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($members as $member): ?>
                        <tr>
                            <td><?= $member['member_id'] ?></td>
                            <td><?= $member['name'] ?></td>
                            <td><?= $member['email'] ?? '-' ?></td>
                            <td><?= $member['phone'] ?? '-' ?></td>
                            <td><?= date('d M Y', strtotime($member['join_date'])) ?></td>
                            <td>
                                <?php if ($member['status'] == 'active'): ?>
                                    <span class="badge bg-success">Active</span>
                                <?php else: ?>
                                    <span class="badge bg-danger">Inactive</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="<?= site_url('members/show/' . $member['member_id']) ?>" class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="View">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="<?= site_url('members/edit/' . $member['member_id']) ?>" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="<?= site_url('commitments/member-commitments/' . $member['member_id']) ?>" class="btn btn-sm btn-success" data-bs-toggle="tooltip" title="Commitments">
                                        <i class="fas fa-handshake"></i>
                                    </a>
                                    <a href="<?= site_url('payments/member-payments/' . $member['member_id']) ?>" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="Payments">
                                        <i class="fas fa-money-bill-wave"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal<?= $member['member_id'] ?>" title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                                
                                <!-- Delete Modal -->
                                <div class="modal fade" id="deleteModal<?= $member['member_id'] ?>" tabindex="-1" aria-labelledby="deleteModalLabel<?= $member['member_id'] ?>" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="deleteModalLabel<?= $member['member_id'] ?>">Confirm Delete</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                Are you sure you want to delete member <strong><?= $member['name'] ?></strong>? This action cannot be undone.
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                <a href="<?= site_url('members/delete/' . $member['member_id']) ?>" class="btn btn-danger">Delete</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?= $this->endSection() ?>
