<?= $this->extend('layout/main') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Collection Summary</h1>
    <a href="<?= site_url('reports/recalculate-all-summaries') ?>" class="btn btn-warning" onclick="return confirm('Are you sure you want to recalculate all summaries? This may take a moment.');">
        <i class="fas fa-sync"></i> Recalculate All Summaries
    </a>
</div>

<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover datatable">
                <thead>
                    <tr>
                        <th>Member</th>
                        <th>Status</th>
                        <th>Total Committed</th>
                        <th>Total Paid</th>
                        <th>Balance</th>
                        <th>Last Payment</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php 
                    $totalCommitted = 0;
                    $totalPaid = 0;
                    $totalBalance = 0;
                    ?>
                    
                    <?php foreach ($summaries as $summary): ?>
                        <?php 
                        $totalCommitted += $summary['total_committed'];
                        $totalPaid += $summary['total_paid'];
                        $totalBalance += $summary['balance'];
                        ?>
                        <tr>
                            <td>
                                <a href="<?= site_url('members/show/' . $summary['member_id']) ?>">
                                    <?= $summary['member_name'] ?>
                                </a>
                            </td>
                            <td>
                                <?php if ($summary['status'] == 'active'): ?>
                                    <span class="badge bg-success">Active</span>
                                <?php else: ?>
                                    <span class="badge bg-danger">Inactive</span>
                                <?php endif; ?>
                            </td>
                            <td><?= number_format($summary['total_committed'], 2) ?></td>
                            <td><?= number_format($summary['total_paid'], 2) ?></td>
                            <td class="<?= $summary['balance'] > 0 ? 'text-danger fw-bold' : 'text-success' ?>">
                                <?= number_format($summary['balance'], 2) ?>
                            </td>
                            <td>
                                <?php if ($summary['last_payment_date']): ?>
                                    <?= date('d M Y', strtotime($summary['last_payment_date'])) ?>
                                    <br>
                                    <small class="text-muted">
                                        Amount: <?= number_format($summary['last_payment_amount'], 2) ?>
                                    </small>
                                <?php else: ?>
                                    <span class="text-muted">No payments</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="<?= site_url('commitments/member-commitments/' . $summary['member_id']) ?>" class="btn btn-sm btn-success" data-bs-toggle="tooltip" title="View Commitments">
                                        <i class="fas fa-handshake"></i>
                                    </a>
                                    <a href="<?= site_url('payments/member-payments/' . $summary['member_id']) ?>" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="View Payments">
                                        <i class="fas fa-money-bill-wave"></i>
                                    </a>
                                    <?php if ($summary['balance'] > 0): ?>
                                        <a href="<?= site_url('payments/new?member_id=' . $summary['member_id']) ?>" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="Record Payment">
                                            <i class="fas fa-plus-circle"></i>
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
                <tfoot>
                    <tr class="table-dark">
                        <th colspan="2">TOTAL</th>
                        <th><?= number_format($totalCommitted, 2) ?></th>
                        <th><?= number_format($totalPaid, 2) ?></th>
                        <th><?= number_format($totalBalance, 2) ?></th>
                        <th colspan="2"></th>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
</div>

<?= $this->endSection() ?>
